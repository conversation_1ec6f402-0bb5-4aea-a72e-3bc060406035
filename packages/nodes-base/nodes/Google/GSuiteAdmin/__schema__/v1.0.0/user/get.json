{"type": "object", "properties": {"agreedToTerms": {"type": "boolean"}, "archived": {"type": "boolean"}, "changePasswordAtNextLogin": {"type": "boolean"}, "creationTime": {"type": "string"}, "customerId": {"type": "string"}, "emails": {"type": "array", "items": {"type": "object", "properties": {"address": {"type": "string"}, "primary": {"type": "boolean"}}}}, "etag": {"type": "string"}, "id": {"type": "string"}, "includeInGlobalAddressList": {"type": "boolean"}, "ipWhitelisted": {"type": "boolean"}, "isAdmin": {"type": "boolean"}, "isDelegatedAdmin": {"type": "boolean"}, "isEnforcedIn2Sv": {"type": "boolean"}, "isEnrolledIn2Sv": {"type": "boolean"}, "isMailboxSetup": {"type": "boolean"}, "kind": {"type": "string"}, "languages": {"type": "array", "items": {"type": "object", "properties": {"languageCode": {"type": "string"}, "preference": {"type": "string"}}}}, "lastLoginTime": {"type": "string"}, "name": {"type": "object", "properties": {"familyName": {"type": "string"}, "fullName": {"type": "string"}, "givenName": {"type": "string"}}}, "orgUnitPath": {"type": "string"}, "primaryEmail": {"type": "string"}, "suspended": {"type": "boolean"}}, "version": 1}