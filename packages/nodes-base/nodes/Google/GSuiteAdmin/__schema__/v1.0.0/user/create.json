{"type": "object", "properties": {"creationTime": {"type": "string"}, "customerId": {"type": "string"}, "etag": {"type": "string"}, "id": {"type": "string"}, "isAdmin": {"type": "boolean"}, "isDelegatedAdmin": {"type": "boolean"}, "isMailboxSetup": {"type": "boolean"}, "kind": {"type": "string"}, "name": {"type": "object", "properties": {"familyName": {"type": "string"}, "givenName": {"type": "string"}}}, "orgUnitPath": {"type": "string"}, "primaryEmail": {"type": "string"}}, "version": 1}