{"nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [120, 700], "id": "0e76b314-4994-4141-975f-9614c6094c80", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"firstName": "NewOne", "lastName": "User", "password": "12345678", "username": "new", "domain": "example.com", "additionalFields": {"changePasswordAtNextLogin": true, "phoneUi": {"phoneValues": [{"value": "******-555-0123"}]}, "emailUi": {"emailValues": [{"address": "<EMAIL>"}]}, "roles": ["groupsAdmin"], "customFields": {"fieldValues": [{"schemaName": "NewTest", "fieldName": "test", "value": "test"}]}}}, "type": "n8n-nodes-base.gSuiteAdmin", "typeVersion": 1, "position": [100, 1100], "id": "54227da8-70ad-456a-8d75-f7e28d514e90", "name": "Create User", "credentials": {"gSuiteAdminOAuth2Api": {"id": "OXfPMaggXFJ0RLkw", "name": "Google Workspace Admin account"}}}], "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Create User", "type": "main", "index": 0}]]}}, "pinData": {"Create User": [{"json": {"kind": "admin#directory#user", "id": "112507770188715525288", "etag": "\"example\"", "primaryEmail": "<EMAIL>", "name": {"givenName": "NewOne", "familyName": "User"}, "isAdmin": false, "isDelegatedAdmin": false, "creationTime": "2024-12-20T20:48:53.000Z", "emails": [{"address": "<EMAIL>", "type": "work"}], "phones": [{"value": "******-555-0123", "primary": false, "type": "work"}], "customerId": "C4444hnz2", "orgUnitPath": "/", "isMailboxSetup": false}}]}}