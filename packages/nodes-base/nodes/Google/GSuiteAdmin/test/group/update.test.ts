import { NodeTestHarness } from '@nodes-testing/node-test-harness';
import nock from 'nock';

describe('Google GSuiteAdmin Node - Update Group', () => {
	beforeEach(() => {
		nock.disableNetConnect();
		nock('https://www.googleapis.com/admin')
			.put('/directory/v1/groups/01302m922p525286')
			.reply(200, {
				kind: 'admin#directory#group',
				id: '01302m922p525286',
				etag: '"example"',
				email: '<EMAIL>',
				name: 'new2',
				description: 'new1',
				adminCreated: true,
				aliases: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
				nonEditableAliases: [
					'<EMAIL>',
					'<EMAIL>',
				],
			});
	});

	new NodeTestHarness().setupTests({
		workflowFiles: ['update.workflow.json'],
	});
});
