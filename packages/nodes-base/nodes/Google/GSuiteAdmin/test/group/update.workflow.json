{"nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [120, 700], "id": "0e76b314-4994-4141-975f-9614c6094c80", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"resource": "group", "operation": "update", "groupId": {"__rl": true, "value": "01302m922p525286", "mode": "list", "cachedResultName": "new"}, "updateFields": {"description": "new1", "email": "<EMAIL>", "name": "new2"}}, "type": "n8n-nodes-base.gSuiteAdmin", "typeVersion": 1, "position": [80, 1100], "id": "013eec82-8d52-4485-88eb-d9caf112d539", "name": "Update Group", "credentials": {"gSuiteAdminOAuth2Api": {"id": "OXfPMaggXFJ0RLkw", "name": "Google Workspace Admin account"}}}], "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Update Group", "type": "main", "index": 0}]]}}, "pinData": {"Update Group": [{"json": {"kind": "admin#directory#group", "id": "01302m922p525286", "etag": "\"example\"", "email": "<EMAIL>", "name": "new2", "description": "new1", "adminCreated": true, "aliases": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "nonEditableAliases": ["<EMAIL>", "<EMAIL>"]}}]}}