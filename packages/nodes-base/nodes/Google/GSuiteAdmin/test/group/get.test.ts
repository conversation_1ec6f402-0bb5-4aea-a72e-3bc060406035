import { NodeTestHarness } from '@nodes-testing/node-test-harness';
import nock from 'nock';

describe('Google GSuiteAdmin Node - Get Group', () => {
	beforeEach(() => {
		nock.disableNetConnect();
		nock('https://www.googleapis.com/admin')
			.get('/directory/v1/groups/01302m922pmp3e4')
			.reply(200, {
				kind: 'admin#directory#group',
				id: '01302m922pmp3e4',
				etag: '"example"',
				email: '<EMAIL>',
				name: 'new2',
				directMembersCount: '2',
				description: 'new1',
				adminCreated: true,
				aliases: ['<EMAIL>', '<EMAIL>', '<EMAIL>'],
				nonEditableAliases: [
					'<EMAIL>',
					'<EMAIL>',
					'<EMAIL>',
					'<EMAIL>',
				],
			});
	});

	new NodeTestHarness().setupTests({
		workflowFiles: ['get.workflow.json'],
	});
});
