{"type": "object", "properties": {"contentDetails": {"type": "object", "properties": {"itemCount": {"type": "integer"}}}, "etag": {"type": "string"}, "id": {"type": "string"}, "kind": {"type": "string"}, "player": {"type": "object", "properties": {"embedHtml": {"type": "string"}}}, "snippet": {"type": "object", "properties": {"channelId": {"type": "string"}, "channelTitle": {"type": "string"}, "description": {"type": "string"}, "localized": {"type": "object", "properties": {"description": {"type": "string"}, "title": {"type": "string"}}}, "publishedAt": {"type": "string"}, "thumbnails": {"type": "object", "properties": {"default": {"type": "object", "properties": {"height": {"type": "integer"}, "url": {"type": "string"}, "width": {"type": "integer"}}}, "high": {"type": "object", "properties": {"height": {"type": "integer"}, "url": {"type": "string"}, "width": {"type": "integer"}}}, "maxres": {"type": "object", "properties": {"height": {"type": "integer"}, "url": {"type": "string"}, "width": {"type": "integer"}}}, "medium": {"type": "object", "properties": {"height": {"type": "integer"}, "url": {"type": "string"}, "width": {"type": "integer"}}}, "standard": {"type": "object", "properties": {"height": {"type": "integer"}, "url": {"type": "string"}, "width": {"type": "integer"}}}}}, "title": {"type": "string"}}}, "status": {"type": "object", "properties": {"privacyStatus": {"type": "string"}}}}, "version": 1}