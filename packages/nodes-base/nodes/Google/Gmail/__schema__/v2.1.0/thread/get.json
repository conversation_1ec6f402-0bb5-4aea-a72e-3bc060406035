{"type": "object", "properties": {"historyId": {"type": "string"}, "id": {"type": "string"}, "messages": {"type": "array", "items": {"type": "object", "properties": {"From": {"type": "string"}, "historyId": {"type": "string"}, "id": {"type": "string"}, "internalDate": {"type": "string"}, "labels": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "string"}, "name": {"type": "string"}}}}, "payload": {"type": "object", "properties": {"mimeType": {"type": "string"}}}, "sizeEstimate": {"type": "integer"}, "snippet": {"type": "string"}, "Subject": {"type": "string"}, "threadId": {"type": "string"}, "To": {"type": "string"}}}}}, "version": 3}