{"name": "My workflow 130", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-60, 100], "id": "636b40bc-2c98-4b9a-8ce2-9d1322294518", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"resource": "draft", "operation": "get", "messageId": "test-draft-id", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [440, 200], "id": "8802fdb5-2741-407b-82a4-ccedc4055076", "name": "Gmail - Drafts - Get", "webhookId": "3b8b38e0-2f4b-40bc-8b67-37e7ea95cb60", "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}}, {"parameters": {"resource": "draft", "operation": "delete", "messageId": "test-draft-id"}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [440, 20], "id": "ed979c3a-b2ea-413e-be63-0392cc1714a5", "name": "Gmail - Drafts - Delete", "webhookId": "3b8b38e0-2f4b-40bc-8b67-37e7ea95cb60", "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}}, {"parameters": {"resource": "draft", "subject": "Test Draft Subject", "message": "Test Draft Message", "options": {"attachmentsUi": {"attachmentsBinary": [{"property": "data"}]}, "bccList": "<EMAIL>", "ccList": "<EMAIL>", "fromAlias": "=<EMAIL>", "replyTo": "<EMAIL>", "threadId": "test-thread-id", "sendTo": "<EMAIL>"}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [840, -180], "id": "45758452-3b5b-478d-aece-001e117ce69d", "name": "Gmail - Drafts - Create", "webhookId": "3b8b38e0-2f4b-40bc-8b67-37e7ea95cb60", "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}}, {"parameters": {"resource": "draft", "operation": "getAll", "returnAll": true, "options": {"dataPropertyAttachmentsPrefixName": "attachment_", "downloadAttachments": true, "includeSpamTrash": true}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [440, 400], "id": "bae81586-7641-4fdc-81a4-0006b289bf9d", "name": "Gmail - Drafts - Get Many", "webhookId": "3b8b38e0-2f4b-40bc-8b67-37e7ea95cb60", "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}}, {"parameters": {"assignments": {"assignments": [{"id": "491590a8-27a6-4d14-b342-493947775d16", "name": "binary", "value": true, "type": "boolean"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [420, -180], "id": "d630c018-1d7b-4779-a280-9f4a21c6a764", "name": "<PERSON>"}, {"parameters": {"operation": "to<PERSON><PERSON>", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [640, -180], "id": "fc3bdb76-c278-44f6-9aac-153b79c8177b", "name": "Convert to File"}], "pinData": {"Gmail - Drafts - Create": [{"json": {"id": "a1b2c3d4e5f6g7h8", "threadId": "a1b2c3d4e5f6g7h8", "labelIds": ["UNREAD", "CATEGORY_PROMOTIONS", "INBOX"], "snippet": "Don't miss our exclusive holiday discounts on all items! Act now before the sale ends.", "payload": {"partId": "", "mimeType": "multipart/alternative", "filename": "", "headers": [{"name": "Delivered-To", "value": "<EMAIL>"}, {"name": "Received", "value": "by 2001:db8::abcd with SMTP id xyz123abc456; Thu, 5 Dec 2024 08:30:00 -0800 (PST)"}, {"name": "X-Google-Smtp-Source", "value": "ABC12345+EXAMPLE123456789"}, {"name": "X-Received", "value": "by ********* with SMTP id 12345abc67890; Thu, 5 Dec 2024 08:30:00 -0800 (PST)"}, {"name": "ARC-Seal", "value": "i=1; a=rsa-sha256; t=1733405400; cv=none; d=example.com; s=arc-20241205; b=ABCDEFG123456="}, {"name": "ARC-Message-Signature", "value": "i=1; a=rsa-sha256; c=relaxed/relaxed; d=example.com; s=arc-20241205; bh=EXAMPLEHASH12345="}, {"name": "ARC-Authentication-Results", "value": "i=1; mx.example.com; dkim=pass header.i=@promotion.example.com; spf=pass smtp.mailfrom=<EMAIL>; dmarc=pass header.from=example.com"}, {"name": "Return-Path", "value": "<<EMAIL>>"}, {"name": "Date", "value": "Thu, 5 Dec 2024 08:30:00 -0800"}, {"name": "From", "value": "Holiday Deals <<EMAIL>>"}, {"name": "To", "value": "<EMAIL>"}, {"name": "Message-ID", "value": "<<EMAIL>>"}, {"name": "Subject", "value": "Exclusive Holiday Discounts!"}, {"name": "MIME-Version", "value": "1.0"}, {"name": "Content-Type", "value": "multipart/alternative; boundary=\"----=_Part_12345_67890.1733405400000\""}], "body": {"size": 0}, "parts": [{"partId": "0", "mimeType": "text/plain", "filename": "", "headers": [{"name": "Content-Type", "value": "text/plain; charset=utf-8"}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "body": {"size": 1234, "data": "VGhpcyBpcyBhbiBleGFtcGxlIG1lc3NhZ2UuIFRoYW5rIHlvdSBmb3Igc2hvcHBpbmcgd2l0aCB1cy4="}}, {"partId": "1", "mimeType": "text/html", "filename": "", "headers": [{"name": "Content-Type", "value": "text/html; charset=\"utf-8\""}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "body": {"size": 5678, "data": "PGRpdiBzdHlsZT0nZm9udC1mYW1pbHk6IEFyaWFsLCBzYW5zLXNlcmlmOyc+VGhpcyBpcyBhbiBleGFtcGxlIGh0bWwgbWVzc2FnZS4gPGI+VGhhbmsgeW91IGZvciBzaG9wcGluZyB3aXRoIHVzLjwvYj48L2Rpdj4="}}]}, "sizeEstimate": 67890, "historyId": "54321", "internalDate": "1733405400000"}}], "Gmail - Drafts - Delete": [{"json": {"success": true}}], "Gmail - Drafts - Get": [{"json": {"threadId": "a1b2c3d4e5f6g7h8", "labelIds": ["UNREAD", "CATEGORY_PROMOTIONS", "INBOX"], "sizeEstimate": 67890, "headers": {"mime-version": "MIME-Version: 1.0", "date": "Date: Fri, 13 Dec 2024 11:15:01 +0100", "message-id": "Message-ID: <<EMAIL>>", "subject": "Subject: Test draft", "from": "From: node qa <<EMAIL>>", "to": "To: <EMAIL>", "content-type": "Content-Type: multipart/alternative; boundary=\"0000000000009d58b60629241a22\""}, "html": "<div dir=\"ltr\">draft body<br></div>\n", "text": "draft body\n", "textAsHtml": "<p>draft body</p>", "subject": "Test draft", "date": "2024-12-13T10:15:01.000Z", "to": {"value": [{"address": "<EMAIL>", "name": ""}], "html": "<span class=\"mp_address_group\"><a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a></span>", "text": "<EMAIL>"}, "from": {"value": [{"address": "<EMAIL>", "name": "node qa"}], "html": "<span class=\"mp_address_group\"><span class=\"mp_address_name\">node qa</span> &lt;<a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a>&gt;</span>", "text": "\"node qa\" <<EMAIL>>"}, "messageId": "a1b2c3d4e5f6g7h8"}}], "Gmail - Drafts - Get Many": [{"json": {"threadId": "a1b2c3d4e5f6g7h8", "labelIds": ["UNREAD", "CATEGORY_PROMOTIONS", "INBOX"], "sizeEstimate": 67890, "headers": {"mime-version": "MIME-Version: 1.0", "date": "Date: Fri, 13 Dec 2024 11:15:01 +0100", "message-id": "Message-ID: <<EMAIL>>", "subject": "Subject: Test draft", "from": "From: node qa <<EMAIL>>", "to": "To: <EMAIL>", "content-type": "Content-Type: multipart/alternative; boundary=\"0000000000009d58b60629241a22\""}, "html": "<div dir=\"ltr\">draft body<br></div>\n", "text": "draft body\n", "textAsHtml": "<p>draft body</p>", "subject": "Test draft", "date": "2024-12-13T10:15:01.000Z", "to": {"value": [{"address": "<EMAIL>", "name": ""}], "html": "<span class=\"mp_address_group\"><a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a></span>", "text": "<EMAIL>"}, "from": {"value": [{"address": "<EMAIL>", "name": "node qa"}], "html": "<span class=\"mp_address_group\"><span class=\"mp_address_name\">node qa</span> &lt;<a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a>&gt;</span>", "text": "\"node qa\" <<EMAIL>>"}, "messageId": "a1b2c3d4e5f6g7h8"}}, {"json": {"threadId": "z9y8x7w6v5u4t3s2", "labelIds": ["UNREAD", "CATEGORY_SOCIAL", "INBOX"], "sizeEstimate": 54321, "headers": {"mime-version": "MIME-Version: 1.0", "date": "Date: Fri, 13 Dec 2024 11:15:01 +0100", "message-id": "Message-ID: <<EMAIL>>", "subject": "Subject: Test draft", "from": "From: node qa <<EMAIL>>", "to": "To: <EMAIL>", "content-type": "Content-Type: multipart/alternative; boundary=\"0000000000009d58b60629241a22\""}, "html": "<div dir=\"ltr\">draft body<br></div>\n", "text": "draft body\n", "textAsHtml": "<p>draft body</p>", "subject": "Test draft", "date": "2024-12-13T10:15:01.000Z", "to": {"value": [{"address": "<EMAIL>", "name": ""}], "html": "<span class=\"mp_address_group\"><a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a></span>", "text": "<EMAIL>"}, "from": {"value": [{"address": "<EMAIL>", "name": "node qa"}], "html": "<span class=\"mp_address_group\"><span class=\"mp_address_name\">node qa</span> &lt;<a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a>&gt;</span>", "text": "\"node qa\" <<EMAIL>>"}, "messageId": "z9y8x7w6v5u4t3s2"}}]}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Gmail - Drafts - Get Many", "type": "main", "index": 0}, {"node": "Gmail - Drafts - Get", "type": "main", "index": 0}, {"node": "Gmail - Drafts - Delete", "type": "main", "index": 0}, {"node": "<PERSON>", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Convert to File", "type": "main", "index": 0}]]}, "Convert to File": {"main": [[{"node": "Gmail - Drafts - Create", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "84fb11a4-4166-45bd-bd9f-60fa378d9e68", "meta": {"instanceId": "27cc9b56542ad45b38725555722c50a1c3fee1670bbb67980558314ee08517c4"}, "id": "09KDcfGmfDrLInDE", "tags": []}