{"name": "Gmail v1 test - message labels", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [1220, 600], "id": "84b20fb7-d041-4a49-b7dc-8a09c1ac0071", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"resource": "messageLabel", "messageId": "test", "labelIds": "={{ ['label1', 'label2'] }}"}, "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [1440, 500], "id": "71a7716d-344b-4050-b2b8-f9f049e46642", "name": "Gmail - Message Labels - Add", "webhookId": "f3cbddc1-3cfa-4217-aa73-f0e5a9309661", "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}}, {"parameters": {"resource": "messageLabel", "operation": "remove", "messageId": "test", "labelIds": "={{ ['label1', 'label2'] }}"}, "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [1440, 700], "id": "c92f2d14-f4a3-48d4-b59b-917d4670163d", "name": "Gmail - Message Labels - Delete", "webhookId": "f3cbddc1-3cfa-4217-aa73-f0e5a9309661", "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}}], "pinData": {"Gmail - Message Labels - Add": [{"json": {"historyId": "54321", "id": "a1b2c3d4e5f6g7h8", "internalDate": "1733405400000", "labelIds": ["UNREAD", "CATEGORY_PROMOTIONS", "INBOX"], "payload": {"body": {"size": 0}, "filename": "", "headers": [{"name": "Delivered-To", "value": "<EMAIL>"}, {"name": "Received", "value": "by 2001:db8::abcd with SMTP id xyz123abc456; Thu, 5 Dec 2024 08:30:00 -0800 (PST)"}, {"name": "X-Google-Smtp-Source", "value": "ABC12345+EXAMPLE123456789"}, {"name": "X-Received", "value": "by ********* with SMTP id 12345abc67890; Thu, 5 Dec 2024 08:30:00 -0800 (PST)"}, {"name": "ARC-Seal", "value": "i=1; a=rsa-sha256; t=1733405400; cv=none; d=example.com; s=arc-20241205; b=ABCDEFG123456="}, {"name": "ARC-Message-Signature", "value": "i=1; a=rsa-sha256; c=relaxed/relaxed; d=example.com; s=arc-20241205; bh=EXAMPLEHASH12345="}, {"name": "ARC-Authentication-Results", "value": "i=1; mx.example.com; dkim=pass header.i=@promotion.example.com; spf=pass smtp.mailfrom=<EMAIL>; dmarc=pass header.from=example.com"}, {"name": "Return-Path", "value": "<<EMAIL>>"}, {"name": "Date", "value": "Thu, 5 Dec 2024 08:30:00 -0800"}, {"name": "From", "value": "Holiday Deals <<EMAIL>>"}, {"name": "To", "value": "<EMAIL>"}, {"name": "Message-ID", "value": "<<EMAIL>>"}, {"name": "Subject", "value": "Exclusive Holiday Discounts!"}, {"name": "MIME-Version", "value": "1.0"}, {"name": "Content-Type", "value": "multipart/alternative; boundary=\"----=_Part_12345_67890.1733405400000\""}], "mimeType": "multipart/alternative", "partId": "", "parts": [{"body": {"data": "VGhpcyBpcyBhbiBleGFtcGxlIG1lc3NhZ2UuIFRoYW5rIHlvdSBmb3Igc2hvcHBpbmcgd2l0aCB1cy4=", "size": 1234}, "filename": "", "headers": [{"name": "Content-Type", "value": "text/plain; charset=utf-8"}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "mimeType": "text/plain", "partId": "0"}, {"body": {"data": "PGRpdiBzdHlsZT0nZm9udC1mYW1pbHk6IEFyaWFsLCBzYW5zLXNlcmlmOyc+VGhpcyBpcyBhbiBleGFtcGxlIGh0bWwgbWVzc2FnZS4gPGI+VGhhbmsgeW91IGZvciBzaG9wcGluZyB3aXRoIHVzLjwvYj48L2Rpdj4=", "size": 5678}, "filename": "", "headers": [{"name": "Content-Type", "value": "text/html; charset=\"utf-8\""}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "mimeType": "text/html", "partId": "1"}]}, "sizeEstimate": 67890, "snippet": "Don't miss our exclusive holiday discounts on all items! Act now before the sale ends.", "threadId": "a1b2c3d4e5f6g7h8"}}], "Gmail - Message Labels - Delete": [{"json": {"historyId": "54321", "id": "a1b2c3d4e5f6g7h8", "internalDate": "1733405400000", "labelIds": ["UNREAD", "CATEGORY_PROMOTIONS", "INBOX"], "payload": {"body": {"size": 0}, "filename": "", "headers": [{"name": "Delivered-To", "value": "<EMAIL>"}, {"name": "Received", "value": "by 2001:db8::abcd with SMTP id xyz123abc456; Thu, 5 Dec 2024 08:30:00 -0800 (PST)"}, {"name": "X-Google-Smtp-Source", "value": "ABC12345+EXAMPLE123456789"}, {"name": "X-Received", "value": "by ********* with SMTP id 12345abc67890; Thu, 5 Dec 2024 08:30:00 -0800 (PST)"}, {"name": "ARC-Seal", "value": "i=1; a=rsa-sha256; t=1733405400; cv=none; d=example.com; s=arc-20241205; b=ABCDEFG123456="}, {"name": "ARC-Message-Signature", "value": "i=1; a=rsa-sha256; c=relaxed/relaxed; d=example.com; s=arc-20241205; bh=EXAMPLEHASH12345="}, {"name": "ARC-Authentication-Results", "value": "i=1; mx.example.com; dkim=pass header.i=@promotion.example.com; spf=pass smtp.mailfrom=<EMAIL>; dmarc=pass header.from=example.com"}, {"name": "Return-Path", "value": "<<EMAIL>>"}, {"name": "Date", "value": "Thu, 5 Dec 2024 08:30:00 -0800"}, {"name": "From", "value": "Holiday Deals <<EMAIL>>"}, {"name": "To", "value": "<EMAIL>"}, {"name": "Message-ID", "value": "<<EMAIL>>"}, {"name": "Subject", "value": "Exclusive Holiday Discounts!"}, {"name": "MIME-Version", "value": "1.0"}, {"name": "Content-Type", "value": "multipart/alternative; boundary=\"----=_Part_12345_67890.1733405400000\""}], "mimeType": "multipart/alternative", "partId": "", "parts": [{"body": {"data": "VGhpcyBpcyBhbiBleGFtcGxlIG1lc3NhZ2UuIFRoYW5rIHlvdSBmb3Igc2hvcHBpbmcgd2l0aCB1cy4=", "size": 1234}, "filename": "", "headers": [{"name": "Content-Type", "value": "text/plain; charset=utf-8"}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "mimeType": "text/plain", "partId": "0"}, {"body": {"data": "PGRpdiBzdHlsZT0nZm9udC1mYW1pbHk6IEFyaWFsLCBzYW5zLXNlcmlmOyc+VGhpcyBpcyBhbiBleGFtcGxlIGh0bWwgbWVzc2FnZS4gPGI+VGhhbmsgeW91IGZvciBzaG9wcGluZyB3aXRoIHVzLjwvYj48L2Rpdj4=", "size": 5678}, "filename": "", "headers": [{"name": "Content-Type", "value": "text/html; charset=\"utf-8\""}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "mimeType": "text/html", "partId": "1"}]}, "sizeEstimate": 67890, "snippet": "Don't miss our exclusive holiday discounts on all items! Act now before the sale ends.", "threadId": "a1b2c3d4e5f6g7h8"}}]}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Gmail - Message Labels - Add", "type": "main", "index": 0}, {"node": "Gmail - Message Labels - Delete", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "59109e41-be89-484a-b8d0-8f5c8f0407f9", "meta": {"templateCredsSetupCompleted": true, "instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "id": "dbQv4DRzYXIcTNNs", "tags": []}