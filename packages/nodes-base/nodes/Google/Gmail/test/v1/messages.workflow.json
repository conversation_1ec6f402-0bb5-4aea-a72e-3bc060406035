{"name": "Gmail v1 test - messages", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-600, 340], "id": "c72f4b22-a803-4b57-9edb-bde633a39f8e", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"resource": "message", "operation": "getAll", "limit": 2, "additionalFields": {"dataPropertyAttachmentsPrefixName": "custom_attachment_", "includeSpamTrash": true}}, "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [-380, 40], "id": "2b668b97-4b65-4484-9f35-9f147e3db2d1", "name": "Gmail - Messages - All", "webhookId": "f3cbddc1-3cfa-4217-aa73-f0e5a9309661", "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}}, {"parameters": {"resource": "message", "subject": "Test Subject", "message": "Test Message", "toList": ["<EMAIL>"], "additionalFields": {"attachmentsUi": {"attachmentsBinary": [{"property": "data"}]}, "bccList": ["<EMAIL>"], "ccList": ["<EMAIL>"]}}, "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [60, 640], "id": "45dbeae7-d365-4f50-987f-e9b43aaa84aa", "name": "Gmail - Messages - Send", "webhookId": "f3cbddc1-3cfa-4217-aa73-f0e5a9309661", "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}}, {"parameters": {"resource": "message", "operation": "delete", "messageId": "test"}, "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [-380, 240], "id": "0bad13bc-b34d-4f40-a709-854bf4a264a3", "name": "Gmail - Messages - Delete", "webhookId": "f3cbddc1-3cfa-4217-aa73-f0e5a9309661", "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}}, {"parameters": {"resource": "message", "operation": "get", "messageId": "test", "additionalFields": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 1, "position": [-380, 440], "id": "14123413-ba9c-4226-9013-2166e42a1adc", "name": "Gmail - Messages - Get", "webhookId": "f3cbddc1-3cfa-4217-aa73-f0e5a9309661", "credentials": {"gmailOAuth2": {"id": "22", "name": "Gmail 0auth"}}}, {"parameters": {"operation": "to<PERSON><PERSON>", "options": {}}, "type": "n8n-nodes-base.convertToFile", "typeVersion": 1.1, "position": [-160, 640], "id": "a8ff6f8b-b23c-4d5a-b926-f0d8f8bdd34d", "name": "Attachment"}, {"parameters": {"assignments": {"assignments": [{"id": "0a4e38fe-ed38-400e-afb1-9d430f167d54", "name": "attachment", "value": true, "type": "boolean"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-380, 640], "id": "03736be9-7d2e-4a06-9911-b9cde3862a83", "name": "<PERSON>"}], "pinData": {"Gmail - Messages - All": [{"json": {"date": "2024-12-13T10:15:01.000Z", "from": {"html": "<span class=\"mp_address_group\"><span class=\"mp_address_name\">node qa</span> &lt;<a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a>&gt;</span>", "text": "\"node qa\" <<EMAIL>>", "value": [{"address": "<EMAIL>", "name": "node qa"}]}, "headers": {"content-type": "Content-Type: multipart/alternative; boundary=\"0000000000009d58b60629241a22\"", "date": "Date: Fri, 13 Dec 2024 11:15:01 +0100", "from": "From: node qa <<EMAIL>>", "message-id": "Message-ID: <<EMAIL>>", "mime-version": "MIME-Version: 1.0", "subject": "Subject: Test draft", "to": "To: <EMAIL>"}, "html": "<div dir=\"ltr\">draft body<br></div>\n", "id": "a1b2c3d4e5f6g7h8", "labelIds": ["UNREAD", "CATEGORY_PROMOTIONS", "INBOX"], "messageId": "<<EMAIL>>", "sizeEstimate": 67890, "subject": "Test draft", "text": "draft body\n", "textAsHtml": "<p>draft body</p>", "threadId": "a1b2c3d4e5f6g7h8", "to": {"html": "<span class=\"mp_address_group\"><a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a></span>", "text": "<EMAIL>", "value": [{"address": "<EMAIL>", "name": ""}]}}}, {"json": {"date": "2024-12-13T10:15:01.000Z", "from": {"html": "<span class=\"mp_address_group\"><span class=\"mp_address_name\">node qa</span> &lt;<a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a>&gt;</span>", "text": "\"node qa\" <<EMAIL>>", "value": [{"address": "<EMAIL>", "name": "node qa"}]}, "headers": {"content-type": "Content-Type: multipart/alternative; boundary=\"0000000000009d58b60629241a22\"", "date": "Date: Fri, 13 Dec 2024 11:15:01 +0100", "from": "From: node qa <<EMAIL>>", "message-id": "Message-ID: <<EMAIL>>", "mime-version": "MIME-Version: 1.0", "subject": "Subject: Test draft", "to": "To: <EMAIL>"}, "html": "<div dir=\"ltr\">draft body<br></div>\n", "id": "z9y8x7w6v5u4t3s2", "labelIds": ["UNREAD", "CATEGORY_SOCIAL", "INBOX"], "messageId": "<<EMAIL>>", "sizeEstimate": 54321, "subject": "Test draft", "text": "draft body\n", "textAsHtml": "<p>draft body</p>", "threadId": "z9y8x7w6v5u4t3s2", "to": {"html": "<span class=\"mp_address_group\"><a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a></span>", "text": "<EMAIL>", "value": [{"address": "<EMAIL>", "name": ""}]}}}], "Gmail - Messages - Send": [{"json": {"historyId": "54321", "id": "a1b2c3d4e5f6g7h8", "internalDate": "1733405400000", "labelIds": ["UNREAD", "CATEGORY_PROMOTIONS", "INBOX"], "payload": {"body": {"size": 0}, "filename": "", "headers": [{"name": "Delivered-To", "value": "<EMAIL>"}, {"name": "Received", "value": "by 2001:db8::abcd with SMTP id xyz123abc456; Thu, 5 Dec 2024 08:30:00 -0800 (PST)"}, {"name": "X-Google-Smtp-Source", "value": "ABC12345+EXAMPLE123456789"}, {"name": "X-Received", "value": "by ********* with SMTP id 12345abc67890; Thu, 5 Dec 2024 08:30:00 -0800 (PST)"}, {"name": "ARC-Seal", "value": "i=1; a=rsa-sha256; t=1733405400; cv=none; d=example.com; s=arc-20241205; b=ABCDEFG123456="}, {"name": "ARC-Message-Signature", "value": "i=1; a=rsa-sha256; c=relaxed/relaxed; d=example.com; s=arc-20241205; bh=EXAMPLEHASH12345="}, {"name": "ARC-Authentication-Results", "value": "i=1; mx.example.com; dkim=pass header.i=@promotion.example.com; spf=pass smtp.mailfrom=<EMAIL>; dmarc=pass header.from=example.com"}, {"name": "Return-Path", "value": "<<EMAIL>>"}, {"name": "Date", "value": "Thu, 5 Dec 2024 08:30:00 -0800"}, {"name": "From", "value": "Holiday Deals <<EMAIL>>"}, {"name": "To", "value": "<EMAIL>"}, {"name": "Message-ID", "value": "<<EMAIL>>"}, {"name": "Subject", "value": "Exclusive Holiday Discounts!"}, {"name": "MIME-Version", "value": "1.0"}, {"name": "Content-Type", "value": "multipart/alternative; boundary=\"----=_Part_12345_67890.1733405400000\""}], "mimeType": "multipart/alternative", "partId": "", "parts": [{"body": {"data": "VGhpcyBpcyBhbiBleGFtcGxlIG1lc3NhZ2UuIFRoYW5rIHlvdSBmb3Igc2hvcHBpbmcgd2l0aCB1cy4=", "size": 1234}, "filename": "", "headers": [{"name": "Content-Type", "value": "text/plain; charset=utf-8"}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "mimeType": "text/plain", "partId": "0"}, {"body": {"data": "PGRpdiBzdHlsZT0nZm9udC1mYW1pbHk6IEFyaWFsLCBzYW5zLXNlcmlmOyc+VGhpcyBpcyBhbiBleGFtcGxlIGh0bWwgbWVzc2FnZS4gPGI+VGhhbmsgeW91IGZvciBzaG9wcGluZyB3aXRoIHVzLjwvYj48L2Rpdj4=", "size": 5678}, "filename": "", "headers": [{"name": "Content-Type", "value": "text/html; charset=\"utf-8\""}, {"name": "Content-Transfer-Encoding", "value": "quoted-printable"}], "mimeType": "text/html", "partId": "1"}]}, "sizeEstimate": 67890, "snippet": "Don't miss our exclusive holiday discounts on all items! Act now before the sale ends.", "threadId": "a1b2c3d4e5f6g7h8"}}], "Gmail - Messages - Delete": [{"json": {"success": true}}], "Gmail - Messages - Get": [{"json": {"date": "2024-12-13T10:15:01.000Z", "from": {"html": "<span class=\"mp_address_group\"><span class=\"mp_address_name\">node qa</span> &lt;<a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a>&gt;</span>", "text": "\"node qa\" <<EMAIL>>", "value": [{"address": "<EMAIL>", "name": "node qa"}]}, "headers": {"content-type": "Content-Type: multipart/alternative; boundary=\"0000000000009d58b60629241a22\"", "date": "Date: Fri, 13 Dec 2024 11:15:01 +0100", "from": "From: node qa <<EMAIL>>", "message-id": "Message-ID: <<EMAIL>>", "mime-version": "MIME-Version: 1.0", "subject": "Subject: Test draft", "to": "To: <EMAIL>"}, "html": "<div dir=\"ltr\">draft body<br></div>\n", "id": "z9y8x7w6v5u4t3s2", "labelIds": ["UNREAD", "CATEGORY_SOCIAL", "INBOX"], "messageId": "<<EMAIL>>", "sizeEstimate": 54321, "subject": "Test draft", "text": "draft body\n", "textAsHtml": "<p>draft body</p>", "threadId": "z9y8x7w6v5u4t3s2", "to": {"html": "<span class=\"mp_address_group\"><a href=\"mailto:<EMAIL>\" class=\"mp_address_email\"><EMAIL></a></span>", "text": "<EMAIL>", "value": [{"address": "<EMAIL>", "name": ""}]}}}]}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "Gmail - Messages - All", "type": "main", "index": 0}, {"node": "Gmail - Messages - Delete", "type": "main", "index": 0}, {"node": "Gmail - Messages - Get", "type": "main", "index": 0}, {"node": "<PERSON>", "type": "main", "index": 0}]]}, "Attachment": {"main": [[{"node": "Gmail - Messages - Send", "type": "main", "index": 0}]]}, "Edit Fields": {"main": [[{"node": "Attachment", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "790b1b2e-c8aa-4d10-b758-017944e6cf51", "meta": {"instanceId": "cb484ba7b742928a2048bf8829668bed5b5ad9787579adea888f05980292a4a7"}, "id": "dbQv4DRzYXIcTNNs", "tags": []}