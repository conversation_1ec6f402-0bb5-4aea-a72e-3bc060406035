{"node": "n8n-nodes-base.googlePerspective", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Analytics", "Utility"], "resources": {"credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/google/oauth-single-service/"}], "primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.googleperspective/"}], "generic": [{"label": "Create a toxic language detector for Telegram in 4 step", "icon": "🤬", "url": "https://n8n.io/blog/create-a-toxic-language-detector-for-telegram/"}]}, "alias": ["Moderation"]}