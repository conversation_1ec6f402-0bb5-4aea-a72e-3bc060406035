{"type": "object", "properties": {"etag": {"type": "string"}, "id": {"type": "string"}, "kind": {"type": "string"}, "links": {"type": "array", "items": {"type": "object", "properties": {"description": {"type": "string"}, "link": {"type": "string"}, "type": {"type": "string"}}}}, "position": {"type": "string"}, "selfLink": {"type": "string"}, "status": {"type": "string"}, "title": {"type": "string"}, "updated": {"type": "string"}, "webViewLink": {"type": "string"}}, "version": 1}