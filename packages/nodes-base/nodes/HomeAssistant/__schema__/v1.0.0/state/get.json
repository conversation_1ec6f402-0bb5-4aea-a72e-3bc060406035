{"type": "object", "properties": {"attributes": {"type": "object", "properties": {"device_class": {"type": "string"}, "friendly_name": {"type": "string"}, "state_class": {"type": "string"}, "unit_of_measurement": {"type": "string"}}}, "context": {"type": "object", "properties": {"id": {"type": "string"}}}, "entity_id": {"type": "string"}, "last_changed": {"type": "string"}, "last_reported": {"type": "string"}, "last_updated": {"type": "string"}, "state": {"type": "string"}}, "version": 1}