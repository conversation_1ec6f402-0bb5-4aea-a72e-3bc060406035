{"node": "n8n-nodes-base.if", "nodeVersion": "1.0", "codexVersion": "1.0", "details": "The IF node can be used to implement binary conditional logic in your workflow. You can set up one-to-many conditions to evaluate each item of data being inputted into the node. That data will either evaluate to TRUE or FALSE and route out of the node accordingly.\n\nThis node has multiple types of conditions: Bool, String, Number, and Date & Time.", "categories": ["Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.if/"}], "generic": [{"label": "Learn to Automate Your Factory's Incident Reporting: A Step by Step Guide", "icon": "🏭", "url": "https://n8n.io/blog/learn-to-automate-your-factorys-incident-reporting-a-step-by-step-guide/"}, {"label": "2021: The Year to Automate the New You with n8n", "icon": "☀️", "url": "https://n8n.io/blog/2021-the-year-to-automate-the-new-you-with-n8n/"}, {"label": "Why business process automation with n8n can change your daily life", "icon": "🧬", "url": "https://n8n.io/blog/why-business-process-automation-with-n8n-can-change-your-daily-life/"}, {"label": "Create a toxic language detector for Telegram in 4 step", "icon": "🤬", "url": "https://n8n.io/blog/create-a-toxic-language-detector-for-telegram/"}, {"label": "6 e-commerce workflows to power up your Shopify s", "icon": "store", "url": "https://n8n.io/blog/no-code-ecommerce-workflow-automations/"}, {"label": "How to build a low-code, self-hosted URL shortener in 3 steps", "icon": "🔗", "url": "https://n8n.io/blog/how-to-build-a-low-code-self-hosted-url-shortener/"}, {"label": "Automate your data processing pipeline in 9 steps", "icon": "⚙️", "url": "https://n8n.io/blog/automate-your-data-processing-pipeline-in-9-steps-with-n8n/"}, {"label": "How to get started with CRM automation (with 3 no-code workflow ideas", "icon": "👥", "url": "https://n8n.io/blog/how-to-get-started-with-crm-automation-and-no-code-workflow-ideas/"}, {"label": "5 tasks you can automate with the new Notion API ", "icon": "⚡️", "url": "https://n8n.io/blog/5-tasks-you-can-automate-with-notion-api/"}, {"label": "15 Google apps you can combine and automate to increase productivity", "icon": "💡", "url": "https://n8n.io/blog/automate-google-apps-for-productivity/"}, {"label": "How to automatically manage contributions to open-source projects", "icon": "🏷️", "url": "https://n8n.io/blog/automation-for-maintainers-of-open-source-projects/"}, {"label": "How uProc scraped a multi-page website with a low-code workflow", "icon": " 🕸️", "url": "https://n8n.io/blog/how-uproc-scraped-a-multi-page-website-with-a-low-code-workflow/"}, {"label": "5 workflow automations for Matter<PERSON> that we love at n8n", "icon": "🤖", "url": "https://n8n.io/blog/5-workflow-automations-for-mattermost-that-we-love-at-n8n/"}, {"label": "Why this Product Manager loves workflow automation with n8n", "icon": "🧠", "url": "https://n8n.io/blog/why-this-product-manager-loves-workflow-automation-with-n8n/"}, {"label": "Sending Automated Congratulations with Google Sheets, Twilio, and n8n ", "icon": "🙌", "url": "https://n8n.io/blog/sending-automated-congratulations-with-google-sheets-twilio-and-n8n/"}, {"label": "How to set up a no-code CI/CD pipeline with GitHub and TravisCI", "icon": "🎡", "url": "https://n8n.io/blog/how-to-set-up-a-ci-cd-pipeline-with-no-code/"}, {"label": "Benefits of automation and n8n: An interview with HubSpot's <PERSON>", "icon": "🎖", "url": "https://n8n.io/blog/benefits-of-automation-and-n8n-an-interview-with-hubspots-hug<PERSON>-durkin/"}, {"label": "7 no-code workflow automations for Amazon Web Services", "url": "https://n8n.io/blog/aws-workflow-automation/"}]}, "alias": ["Router", "Filter", "Condition", "Logic", "Boolean", "Branch"], "subcategories": {"Core Nodes": ["Flow"]}}