{"name": "Filter test", "nodes": [{"parameters": {}, "id": "96490c63-a3f4-4923-b969-8f9adcbb1bbb", "name": "When clicking \"Execute Workflow\"", "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-160, 300]}, {"parameters": {"conditions": {"options": {"caseSensitive": false, "leftValue": ""}, "conditions": [{"leftValue": "={{ $json.firstname }}", "rightValue": "s", "operator": {"type": "string", "operation": "startsWith"}}, {"leftValue": "={{ $json.lastname }}", "rightValue": "", "operator": {"type": "any", "operation": "exists"}}, {"leftValue": "={{ $json.email }}", "rightValue": "@yahoo.com", "operator": {"type": "string", "operation": "endsWith"}}], "combinator": "and"}, "options": {"caseSensitive": false}}, "id": "48399b31-219a-42fa-bb5b-380dbb4a2e7d", "name": "If", "type": "n8n-nodes-base.if", "typeVersion": 2, "position": [260, 300]}, {"parameters": {}, "id": "8a59a941-bafd-46a6-8692-878de715d912", "name": "false", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [560, 440]}, {"parameters": {}, "id": "b82546fa-f9e9-4fa3-9dcb-e2c94a3784de", "name": "Then", "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [560, 140]}, {"parameters": {"jsCode": "return [\n  {\n    \"email\": \"<PERSON>@yahoo.com\",\n    \"firstname\": \"<PERSON>\",\n    \"lastname\": \"<PERSON>\"\n  },\n  {\n    \"email\": \"<PERSON>@yahoo.com\",\n    \"firstname\": \"<PERSON>\",\n    \"lastname\": \"<PERSON>rom<PERSON>\"\n  },\n  {\n    \"email\": \"<EMAIL>\",\n    \"firstname\": \"<PERSON>\",\n    \"lastname\": \"<PERSON>\"\n  }\n]"}, "id": "674c5688-ac03-49a7-83fb-62460a10cc10", "name": "Code", "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [60, 300]}], "pinData": {"Then": [{"json": {"email": "<PERSON>@yahoo.com", "firstname": "<PERSON>", "lastname": "<PERSON>"}}, {"json": {"email": "<EMAIL>", "firstname": "<PERSON>", "lastname": "Tromp"}}], "false": [{"json": {"email": "<EMAIL>", "firstname": "<PERSON>", "lastname": "<PERSON>"}}]}, "connections": {"When clicking \"Execute Workflow\"": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "If": {"main": [[{"node": "Then", "type": "main", "index": 0}], [{"node": "false", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "If", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "48c6a584-e79b-4ce4-ab4b-2b4ab663b89d", "id": "BWUTRs5RHxVgQ4uT", "meta": {"instanceId": "78577815012af39cf16dad7a787b0898c42fb7514b8a7f99b2136862c2af502c"}, "tags": []}