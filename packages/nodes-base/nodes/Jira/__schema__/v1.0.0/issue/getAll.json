{"type": "object", "properties": {"expand": {"type": "string"}, "fields": {"type": "object", "properties": {"issuetype": {"type": "object", "properties": {"avatarId": {"type": "integer"}, "description": {"type": "string"}, "entityId": {"type": "string"}, "hierarchyLevel": {"type": "integer"}, "iconUrl": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "self": {"type": "string"}, "subtask": {"type": "boolean"}}}, "priority": {"type": "object", "properties": {"iconUrl": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "self": {"type": "string"}}}, "status": {"type": "object", "properties": {"description": {"type": "string"}, "iconUrl": {"type": "string"}, "id": {"type": "string"}, "name": {"type": "string"}, "self": {"type": "string"}, "statusCategory": {"type": "object", "properties": {"colorName": {"type": "string"}, "id": {"type": "integer"}, "key": {"type": "string"}, "name": {"type": "string"}, "self": {"type": "string"}}}}}, "summary": {"type": "string"}}}, "id": {"type": "string"}, "key": {"type": "string"}, "self": {"type": "string"}}, "version": 5}