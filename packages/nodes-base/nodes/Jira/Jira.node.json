{"node": "n8n-nodes-base.jira", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Development", "Productivity"], "resources": {"credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/jira/"}], "primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.jira/"}], "generic": [{"label": "5 workflow automations for Matter<PERSON> that we love at n8n", "icon": "🤖", "url": "https://n8n.io/blog/5-workflow-automations-for-mattermost-that-we-love-at-n8n/"}, {"label": "How to automate every step of an incident response workflow", "url": "https://n8n.io/blog/creating-custom-incident-response-workflows-with-n8n/"}]}}