import { NodeTestHarness } from '@nodes-testing/node-test-harness';
import type { WorkflowTestData } from 'n8n-workflow';

jest.mock('ics', () => {
	const ics = jest.requireActual('ics');
	return {
		...ics,
		createEvent(attributes: any, cb: () => {}) {
			attributes.uid = 'test-uid';
			attributes.timestamp = '20250424T135100Z';
			return ics.createEvent(attributes, cb);
		},
	};
});

describe('iCalendar Node', () => {
	const testHarness = new NodeTestHarness();
	const tests: WorkflowTestData[] = [
		{
			description: 'nodes/ICalendar/test/node/workflow.iCalendar.json',
			input: {
				workflowData: testHarness.readWorkflowJSON('workflow.iCalendar.json'),
			},
			output: {
				assertBinaryData: true,
				nodeData: {
					iCalendar: [
						[
							{
								json: {},
								binary: {
									data: {
										mimeType: 'text/calendar',
										fileType: 'text',
										fileExtension: 'ics',
										data: '********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
										fileName: 'event.ics',
										fileSize: '346 B',
									},
								},
							},
						],
					],
				},
			},
		},
	];

	for (const testData of tests) {
		testHarness.setupTest(testData);
	}
});
