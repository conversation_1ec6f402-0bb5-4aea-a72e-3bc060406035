{"node": "n8n-nodes-base.htmlExtract", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.html/"}], "generic": [{"label": "Why business process automation with n8n can change your daily life", "icon": "🧬", "url": "https://n8n.io/blog/why-business-process-automation-with-n8n-can-change-your-daily-life/"}, {"label": "5 tasks you can automate with the new Notion API ", "icon": "⚡️", "url": "https://n8n.io/blog/5-tasks-you-can-automate-with-notion-api/"}, {"label": "How uProc scraped a multi-page website with a low-code workflow", "icon": " 🕸️", "url": "https://n8n.io/blog/how-uproc-scraped-a-multi-page-website-with-a-low-code-workflow/"}, {"label": "How to use the HTTP Request Node - The Swiss Army Knife for Workflow Automation", "icon": "🧰", "url": "https://n8n.io/blog/how-to-use-the-http-request-node-the-swiss-army-knife-for-workflow-automation/"}]}, "subcategories": {"Core Nodes": ["Helpers", "Data Transformation"]}}