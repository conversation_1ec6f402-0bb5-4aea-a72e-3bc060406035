{"type": "object", "properties": {"associations": {"type": "object", "properties": {"companyIds": {"type": "array", "items": {"type": "integer"}}, "contactIds": {"type": "array", "items": {"type": "integer"}}, "dealIds": {"type": "array", "items": {"type": "integer"}}, "ownerIds": {"type": "array", "items": {"type": "integer"}}, "ticketIds": {"type": "array", "items": {"type": "integer"}}}}, "engagement": {"type": "object", "properties": {"active": {"type": "boolean"}, "bodyPreview": {"type": "string"}, "bodyPreviewHtml": {"type": "string"}, "bodyPreviewIsTruncated": {"type": "boolean"}, "createdAt": {"type": "integer"}, "id": {"type": "integer"}, "lastUpdated": {"type": "integer"}, "portalId": {"type": "integer"}, "timestamp": {"type": "integer"}, "type": {"type": "string"}}}, "metadata": {"type": "object", "properties": {"status": {"type": "string"}}}}, "version": 1}