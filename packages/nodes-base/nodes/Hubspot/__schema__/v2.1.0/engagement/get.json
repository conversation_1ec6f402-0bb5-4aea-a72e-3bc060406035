{"type": "object", "properties": {"associations": {"type": "object", "properties": {"companyIds": {"type": "array", "items": {"type": "integer"}}, "contactIds": {"type": "array", "items": {"type": "integer"}}, "dealIds": {"type": "array", "items": {"type": "integer"}}, "ownerIds": {"type": "array", "items": {"type": "integer"}}, "ticketIds": {"type": "array", "items": {"type": "integer"}}}}, "attachments": {"type": "array", "items": {"type": "object", "properties": {"id": {"type": "integer"}}}}, "engagement": {"type": "object", "properties": {"active": {"type": "boolean"}, "allAccessibleTeamIds": {"type": "array", "items": {"type": "integer"}}, "bodyPreview": {"type": "string"}, "bodyPreviewHtml": {"type": "string"}, "bodyPreviewIsTruncated": {"type": "boolean"}, "createdAt": {"type": "integer"}, "createdBy": {"type": "integer"}, "id": {"type": "integer"}, "lastUpdated": {"type": "integer"}, "modifiedBy": {"type": "integer"}, "ownerId": {"type": "integer"}, "portalId": {"type": "integer"}, "queueMembershipIds": {"type": "array", "items": {"type": "integer"}}, "source": {"type": "string"}, "timestamp": {"type": "integer"}, "type": {"type": "string"}, "uid": {"type": "string"}}}, "metadata": {"type": "object", "properties": {"cc": {"type": "array", "items": {"type": "object", "properties": {"email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "raw": {"type": "string"}}}}, "html": {"type": "string"}, "ownerIdsCc": {"type": "array", "items": {"type": "integer"}}, "ownerIdsFrom": {"type": "array", "items": {"type": "integer"}}, "ownerIdsTo": {"type": "array", "items": {"type": "integer"}}, "status": {"type": "string"}, "subject": {"type": "string"}, "to": {"type": "array", "items": {"type": "object", "properties": {"email": {"type": "string"}, "firstName": {"type": "string"}, "lastName": {"type": "string"}, "raw": {"type": "string"}}}}}}}, "version": 1}