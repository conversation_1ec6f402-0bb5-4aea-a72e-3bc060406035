{"type": "object", "properties": {"companyId": {"type": "integer"}, "isDeleted": {"type": "boolean"}, "portalId": {"type": "integer"}, "properties": {"type": "object", "properties": {"createdate": {"type": "object", "properties": {"source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "null"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}, "hs_lastmodifieddate": {"type": "object", "properties": {"source": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "null"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}, "hs_object_id": {"type": "object", "properties": {"source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "null"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}, "hs_object_source": {"type": "object", "properties": {"source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "null"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}, "hs_object_source_id": {"type": "object", "properties": {"source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "null"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}, "hs_object_source_label": {"type": "object", "properties": {"source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "null"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}, "hs_pipeline": {"type": "object", "properties": {"source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "null"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}, "lifecyclestage": {"type": "object", "properties": {"source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "null"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}}}}, "version": 1}