{"type": "object", "properties": {"companyId": {"type": "integer"}, "isDeleted": {"type": "boolean"}, "portalId": {"type": "integer"}, "properties": {"type": "object", "properties": {"name": {"type": "object", "properties": {"source": {"type": "string"}, "timestamp": {"type": "integer"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}}}}, "version": 5}