{"type": "object", "properties": {"dealId": {"type": "integer"}, "isDeleted": {"type": "boolean"}, "portalId": {"type": "integer"}, "properties": {"type": "object", "properties": {"amount": {"type": "object", "properties": {"source": {"type": "string"}, "timestamp": {"type": "integer"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "sourceUpstreamDeployable": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}, "createdate": {"type": "object", "properties": {"source": {"type": "string"}, "timestamp": {"type": "integer"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "sourceUpstreamDeployable": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}, "dealname": {"type": "object", "properties": {"source": {"type": "string"}, "timestamp": {"type": "integer"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "sourceUpstreamDeployable": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}, "dealstage": {"type": "object", "properties": {"source": {"type": "string"}, "timestamp": {"type": "integer"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "sourceUpstreamDeployable": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}, "hubspot_owner_id": {"type": "object", "properties": {"source": {"type": "string"}, "sourceId": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "integer"}, "value": {"type": "string"}, "versions": {"type": "array", "items": {"type": "object", "properties": {"name": {"type": "string"}, "requestId": {"type": "string"}, "source": {"type": "string"}, "sourceId": {"type": "string"}, "sourceUpstreamDeployable": {"type": "string"}, "timestamp": {"type": "integer"}, "updatedByUserId": {"type": "integer"}, "useTimestampAsPersistenceTimestamp": {"type": "boolean"}, "value": {"type": "string"}}}}}}}}}, "version": 5}