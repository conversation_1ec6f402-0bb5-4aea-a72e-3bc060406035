{"name": "Hubspot contacts", "nodes": [{"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [400, -368], "id": "9b86caa6-4892-4eff-b513-3391bbd36e11", "name": "When clicking ‘Execute workflow’"}, {"parameters": {"operation": "get", "contactId": {"__rl": true, "value": "123", "mode": "id"}, "additionalFields": {"formSubmissionMode": "newest", "listMemberships": true}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [624, -656], "id": "ec2ae603-783d-4a3a-82b8-1a7049616df6", "name": "HubSpot - Contact - Get", "credentials": {"hubspotApi": {"id": "EDJsHUkhqof5gr15", "name": "HubSpot account"}}}, {"parameters": {"operation": "delete", "contactId": {"__rl": true, "value": "123", "mode": "id"}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [624, -1040], "id": "b593dd98-8bd0-4a13-ac44-c732835c2354", "name": "HubSpot - Contact - Delete", "credentials": {"hubspotApi": {"id": "EDJsHUkhqof5gr15", "name": "HubSpot account"}}}, {"parameters": {"email": "<PERSON><PERSON>@n8n.io", "additionalFields": {"annualRevenue": 123, "associatedCompanyId": "=123", "city": "Gent", "closeDate": "2025-04-17T00:00:00", "customPropertiesUi": {"customPropertiesValues": [{"property": "=test_custom_prop_name", "value": "test custom prop value"}]}, "firstName": "<PERSON>", "postalCode": "9000", "websiteUrl": "example.com", "workEmail": "<PERSON><PERSON>@n8n.io"}, "options": {}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [624, -848], "id": "29e2d75f-d62d-43ad-8162-75c4fa7dbc1d", "name": "HubSpot - Contact - Create", "credentials": {"hubspotApi": {"id": "EDJsHUkhqof5gr15", "name": "HubSpot account"}}}, {"parameters": {"operation": "getAll", "limit": 2, "additionalFields": {}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [624, -464], "id": "1abd78f0-cf23-44e5-8575-791de126a05a", "name": "HubSpot - Contact - Get Many", "credentials": {"hubspotApi": {"id": "EDJsHUkhqof5gr15", "name": "HubSpot account"}}}, {"parameters": {"operation": "getRecentlyCreatedUpdated", "limit": 2, "additionalFields": {}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [624, -272], "id": "6c0a7374-7d37-4ff1-b66b-84d1e27cf1b8", "name": "HubSpot - Contact - Get Recently Updated", "credentials": {"hubspotApi": {"id": "EDJsHUkhqof5gr15", "name": "HubSpot account"}}}, {"parameters": {"operation": "search", "limit": 2, "filterGroupsUi": {"filterGroupsValues": [{"filtersUi": {"filterValues": [{"propertyName": "=firstname", "value": "<PERSON>"}, {"propertyName": "=lastname", "value": "<PERSON><PERSON>"}]}}, {"filtersUi": {"filterValues": [{"propertyName": "=email", "operator": "CONTAINS_TOKEN", "value": "n8n.io"}]}}]}, "additionalFields": {"direction": "ASCENDING"}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [624, -80], "id": "5b28b56d-bc71-41db-a8ef-03a4c4827038", "name": "HubSpot - Contact - Search", "credentials": {"hubspotApi": {"id": "EDJsHUkhqof5gr15", "name": "HubSpot account"}}}, {"parameters": {"email": "<EMAIL>", "additionalFields": {}, "options": {}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [624, 128], "id": "24dd6489-8372-43fb-877b-7bbfedbdaa9a", "name": "HubSpot - Contact - Create - Without properties", "credentials": {"hubspotApi": {"id": "EDJsHUkhqof5gr15", "name": "HubSpot account"}}}, {"parameters": {"email": "<EMAIL>", "additionalFields": {"buyingRole": ["CHAMPION", "INFLUENCER"], "countryRegionCode": "US", "emailCustomerQuarantinedReason": "BLOCKLIST_REMEDIATION", "employmentRole": "consulting", "enrichedEmailBounceDetected": true, "employmentSeniority": "director", "employmentSubRole": "account_manager", "inferredLanguageCodes": "en", "latestTrafficSource": "OTHER_CAMPAIGNS", "latestTrafficSourceDate": "2025-01-01T00:00:00", "linkedinUrl": "https://linkedin.com/foo", "memberEmail": "<EMAIL>", "militaryStatus": "Foo", "persona": "persona_1", "prospectingAgentLastEnrolled": "2025-02-01T00:00:00", "prospectingAgentTotalEnrolledCount": 2, "stateRegionCode": "CA", "timeZone": "us_slash_pacific", "whatsappPhoneNumber": "*********"}, "options": {}}, "type": "n8n-nodes-base.hubspot", "typeVersion": 2.1, "position": [624, 320], "id": "e857588e-3509-47dc-a43c-2889b3b07a01", "name": "HubSpot - Contact - Create - With new properties", "credentials": {"hubspotApi": {"id": "EDJsHUkhqof5gr15", "name": "HubSpot account"}}}], "pinData": {"HubSpot - Contact - Get": [{"json": {"addedAt": *************, "canonical-vid": 204727, "form-submissions": [], "identity-profiles": [{"deleted-changed-timestamp": 0, "identities": [{"timestamp": *************, "type": "LEAD_GUID", "value": "f9d728f1-dff1-49b0-9caa-247dbdf5b8b7"}, {"timestamp": *************, "type": "EMAIL", "value": "<EMAIL>"}], "saved-at-timestamp": *************, "vid": 204727}], "is-contact": true, "merge-audits": [], "merged-vids": [], "portal-id": 62515, "properties": {"company": {"value": ""}, "firstname": {"value": "<PERSON>"}, "lastmodifieddate": {"value": "*************"}, "lastname": {"value": "Record"}}, "vid": 204727}}], "HubSpot - Contact - Delete": [{"json": {"addedAt": *************, "canonical-vid": 204727, "form-submissions": [], "identity-profiles": [{"deleted-changed-timestamp": 0, "identities": [{"timestamp": *************, "type": "LEAD_GUID", "value": "f9d728f1-dff1-49b0-9caa-247dbdf5b8b7"}, {"timestamp": *************, "type": "EMAIL", "value": "<EMAIL>"}], "saved-at-timestamp": *************, "vid": 204727}], "is-contact": true, "merge-audits": [], "merged-vids": [], "portal-id": 62515, "properties": {"company": {"value": ""}, "firstname": {"value": "<PERSON>"}, "lastmodifieddate": {"value": "*************"}, "lastname": {"value": "Record"}}, "vid": 204727}}], "HubSpot - Contact - Create": [{"json": {"addedAt": *************, "canonical-vid": 204727, "form-submissions": [], "identity-profiles": [{"deleted-changed-timestamp": 0, "identities": [{"timestamp": *************, "type": "LEAD_GUID", "value": "f9d728f1-dff1-49b0-9caa-247dbdf5b8b7"}, {"timestamp": *************, "type": "EMAIL", "value": "<EMAIL>"}], "saved-at-timestamp": *************, "vid": 204727}], "is-contact": true, "merge-audits": [], "merged-vids": [], "portal-id": 62515, "properties": {"company": {"value": ""}, "firstname": {"value": "<PERSON>"}, "lastmodifieddate": {"value": "*************"}, "lastname": {"value": "Record"}}, "vid": 204727}}], "HubSpot - Contact - Get Many": [{"json": {"addedAt": *************, "canonical-vid": 204727, "form-submissions": [], "identity-profiles": [{"deleted-changed-timestamp": 0, "identities": [{"timestamp": *************, "type": "LEAD_GUID", "value": "f9d728f1-dff1-49b0-9caa-247dbdf5b8b7"}, {"timestamp": *************, "type": "EMAIL", "value": "<EMAIL>"}], "saved-at-timestamp": *************, "vid": 204727}], "is-contact": true, "merge-audits": [], "merged-vids": [], "portal-id": 62515, "properties": {"company": {"value": ""}, "firstname": {"value": "<PERSON>"}, "lastmodifieddate": {"value": "*************"}, "lastname": {"value": "Record"}}, "vid": 204727}}, {"json": {"addedAt": 1392643921079, "canonical-vid": 207303, "form-submissions": [], "identity-profiles": [{"deleted-changed-timestamp": 0, "identities": [{"timestamp": 1392643921079, "type": "EMAIL", "value": "<EMAIL>"}, {"timestamp": 1392643921082, "type": "LEAD_GUID", "value": "058378c6-9513-43e1-a13a-43a98d47aa22"}], "saved-at-timestamp": 1392643921090, "vid": 207303}], "is-contact": true, "merge-audits": [], "merged-vids": [], "portal-id": 62515, "properties": {"firstname": {"value": "Ff_FirstName_0"}, "lastmodifieddate": {"value": "1479148429488"}, "lastname": {"value": "Ff_LastName_0"}}, "vid": 207303}}], "HubSpot - Contact - Get Recently Updated": [{"json": {"addedAt": *************, "canonical-vid": 204727, "form-submissions": [], "identity-profiles": [{"deleted-changed-timestamp": 0, "identities": [{"timestamp": *************, "type": "LEAD_GUID", "value": "f9d728f1-dff1-49b0-9caa-247dbdf5b8b7"}, {"timestamp": *************, "type": "EMAIL", "value": "<EMAIL>"}], "saved-at-timestamp": *************, "vid": 204727}], "is-contact": true, "merge-audits": [], "merged-vids": [], "portal-id": 62515, "properties": {"company": {"value": ""}, "firstname": {"value": "<PERSON>"}, "lastmodifieddate": {"value": "*************"}, "lastname": {"value": "Record"}}, "vid": 204727}}, {"json": {"addedAt": 1392643921079, "canonical-vid": 207303, "form-submissions": [], "identity-profiles": [{"deleted-changed-timestamp": 0, "identities": [{"timestamp": 1392643921079, "type": "EMAIL", "value": "<EMAIL>"}, {"timestamp": 1392643921082, "type": "LEAD_GUID", "value": "058378c6-9513-43e1-a13a-43a98d47aa22"}], "saved-at-timestamp": 1392643921090, "vid": 207303}], "is-contact": true, "merge-audits": [], "merged-vids": [], "portal-id": 62515, "properties": {"firstname": {"value": "Ff_FirstName_0"}, "lastmodifieddate": {"value": "1479148429488"}, "lastname": {"value": "Ff_LastName_0"}}, "vid": 207303}}], "HubSpot - Contact - Search": [{"json": {"archived": false, "archivedAt": "2025-04-16T18:54:48.554Z", "createdAt": "2025-04-16T18:54:48.554Z", "id": "512", "objectWriteTraceId": "string", "properties": {"email": "<EMAIL>", "firstname": "<PERSON>", "lastname": "S."}, "propertiesWithHistory": {"additionalProp1": [{"sourceId": "string", "sourceLabel": "string", "sourceType": "string", "timestamp": "2025-04-16T18:54:48.554Z", "updatedByUserId": 0, "value": "string"}], "additionalProp2": [{"sourceId": "string", "sourceLabel": "string", "sourceType": "string", "timestamp": "2025-04-16T18:54:48.554Z", "updatedByUserId": 0, "value": "string"}], "additionalProp3": [{"sourceId": "string", "sourceLabel": "string", "sourceType": "string", "timestamp": "2025-04-16T18:54:48.554Z", "updatedByUserId": 0, "value": "string"}]}, "updatedAt": "2025-04-16T18:54:48.554Z"}}], "HubSpot - Contact - Create - Without properties": [{"json": {"addedAt": *************, "canonical-vid": 204727, "form-submissions": [], "identity-profiles": [{"deleted-changed-timestamp": 0, "identities": [{"timestamp": *************, "type": "LEAD_GUID", "value": "f9d728f1-dff1-49b0-9caa-247dbdf5b8b7"}, {"timestamp": *************, "type": "EMAIL", "value": "<EMAIL>"}], "saved-at-timestamp": *************, "vid": 204727}], "is-contact": true, "merge-audits": [], "merged-vids": [], "portal-id": 62515, "properties": {"company": {"value": ""}, "firstname": {"value": "<PERSON>"}, "lastmodifieddate": {"value": "*************"}, "lastname": {"value": "Record"}}, "vid": 204727}}], "HubSpot - Contact - Create - With new properties": [{"json": {"addedAt": *************, "canonical-vid": 204727, "form-submissions": [], "identity-profiles": [{"deleted-changed-timestamp": 0, "identities": [{"timestamp": *************, "type": "LEAD_GUID", "value": "f9d728f1-dff1-49b0-9caa-247dbdf5b8b7"}, {"timestamp": *************, "type": "EMAIL", "value": "<EMAIL>"}], "saved-at-timestamp": *************, "vid": 204727}], "is-contact": true, "merge-audits": [], "merged-vids": [], "portal-id": 62515, "properties": {"company": {"value": ""}, "firstname": {"value": "<PERSON>"}, "lastmodifieddate": {"value": "*************"}, "lastname": {"value": "Record"}}, "vid": 204727}}]}, "connections": {"When clicking ‘Execute workflow’": {"main": [[{"node": "HubSpot - Contact - Delete", "type": "main", "index": 0}, {"node": "HubSpot - Contact - Create", "type": "main", "index": 0}, {"node": "HubSpot - Contact - Get", "type": "main", "index": 0}, {"node": "HubSpot - Contact - Get Many", "type": "main", "index": 0}, {"node": "HubSpot - Contact - Get Recently Updated", "type": "main", "index": 0}, {"node": "HubSpot - Contact - Search", "type": "main", "index": 0}, {"node": "HubSpot - Contact - Create - Without properties", "type": "main", "index": 0}, {"node": "HubSpot - Contact - Create - With new properties", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "2893ac70-b652-4ada-83de-e1eb3944b0ef", "meta": {"templateCredsSetupCompleted": true, "instanceId": "eeda9e3069aca300d1dfceeb64beb5b53d715db44a50461bbc5cb0cf6daa01e3"}, "id": "ufE0Naih6reFUYf3", "tags": []}