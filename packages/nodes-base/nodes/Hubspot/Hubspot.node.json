{"node": "n8n-nodes-base.hubspot", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Sales"], "resources": {"credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/hubspot/"}], "primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/app-nodes/n8n-nodes-base.hubspot/"}], "generic": [{"label": "How to synchronize data between two systems (one-way vs. two-way sync", "icon": "🏬", "url": "https://n8n.io/blog/how-to-sync-data-between-two-systems/"}, {"label": "How to get started with CRM automation (with 3 no-code workflow ideas", "icon": "👥", "url": "https://n8n.io/blog/how-to-get-started-with-crm-automation-and-no-code-workflow-ideas/"}, {"label": "Hey founders! Your business doesn't need you to operate", "icon": " 🖥️", "url": "https://n8n.io/blog/your-business-doesnt-need-you-to-operate/"}, {"label": "Benefits of automation and n8n: An interview with HubSpot's <PERSON>", "icon": "🎖", "url": "https://n8n.io/blog/benefits-of-automation-and-n8n-an-interview-with-hubspots-hug<PERSON>-durkin/"}, {"label": "How Goomer automated their operations with over 200 n8n workflows", "icon": "🛵", "url": "https://n8n.io/blog/how-goomer-automated-their-operations-with-over-200-n8n-workflows/"}]}}