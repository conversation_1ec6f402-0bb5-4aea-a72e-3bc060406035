{"type": "object", "properties": {"_links": {"type": "object", "properties": {"award_emoji": {"type": "string"}, "closed_as_duplicate_of": {"type": "null"}, "notes": {"type": "string"}, "project": {"type": "string"}, "self": {"type": "string"}}}, "assignee": {"type": "object", "properties": {"avatar_url": {"type": "string"}, "id": {"type": "integer"}, "locked": {"type": "boolean"}, "name": {"type": "string"}, "state": {"type": "string"}, "username": {"type": "string"}, "web_url": {"type": "string"}}}, "assignees": {"type": "array", "items": {"type": "object", "properties": {"avatar_url": {"type": "string"}, "id": {"type": "integer"}, "locked": {"type": "boolean"}, "name": {"type": "string"}, "state": {"type": "string"}, "username": {"type": "string"}, "web_url": {"type": "string"}}}}, "author": {"type": "object", "properties": {"id": {"type": "integer"}, "locked": {"type": "boolean"}, "name": {"type": "string"}, "state": {"type": "string"}, "username": {"type": "string"}, "web_url": {"type": "string"}}}, "blocking_issues_count": {"type": "integer"}, "confidential": {"type": "boolean"}, "created_at": {"type": "string"}, "downvotes": {"type": "integer"}, "epic": {"type": "object", "properties": {"group_id": {"type": "integer"}, "id": {"type": "integer"}, "iid": {"type": "integer"}, "title": {"type": "string"}, "url": {"type": "string"}}}, "has_tasks": {"type": "boolean"}, "id": {"type": "integer"}, "iid": {"type": "integer"}, "imported": {"type": "boolean"}, "imported_from": {"type": "string"}, "issue_type": {"type": "string"}, "iteration": {"type": "object", "properties": {"created_at": {"type": "string"}, "description": {"type": "string"}, "due_date": {"type": "string"}, "group_id": {"type": "integer"}, "id": {"type": "integer"}, "iid": {"type": "integer"}, "sequence": {"type": "integer"}, "start_date": {"type": "string"}, "state": {"type": "integer"}, "title": {"type": "string"}, "updated_at": {"type": "string"}, "web_url": {"type": "string"}}}, "labels": {"type": "array", "items": {"type": "string"}}, "merge_requests_count": {"type": "integer"}, "moved_to_id": {"type": "null"}, "project_id": {"type": "integer"}, "references": {"type": "object", "properties": {"full": {"type": "string"}, "relative": {"type": "string"}, "short": {"type": "string"}}}, "service_desk_reply_to": {"type": "null"}, "severity": {"type": "string"}, "state": {"type": "string"}, "subscribed": {"type": "boolean"}, "task_completion_status": {"type": "object", "properties": {"completed_count": {"type": "integer"}, "count": {"type": "integer"}}}, "task_status": {"type": "string"}, "time_stats": {"type": "object", "properties": {"time_estimate": {"type": "integer"}, "total_time_spent": {"type": "integer"}}}, "title": {"type": "string"}, "type": {"type": "string"}, "updated_at": {"type": "string"}, "upvotes": {"type": "integer"}, "user_notes_count": {"type": "integer"}, "web_url": {"type": "string"}}, "version": 1}