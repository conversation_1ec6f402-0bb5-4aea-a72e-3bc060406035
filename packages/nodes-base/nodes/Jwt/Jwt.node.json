{"node": "n8n-nodes-base.jwt", "nodeVersion": "1.0", "codexVersion": "1.0", "categories": ["Development"], "alias": ["Token", "Key", "JSON", "Payload", "Sign", "Verify", "Decode"], "resources": {"credentialDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/credentials/jwt/"}], "primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.jwt/"}]}}