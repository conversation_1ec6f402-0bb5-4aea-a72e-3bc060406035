{"node": "n8n-nodes-base.itemLists", "nodeVersion": "1.0", "codexVersion": "1.0", "details": "", "categories": ["Core <PERSON>"], "resources": {"primaryDocumentation": [{"url": "https://docs.n8n.io/integrations/builtin/core-nodes/n8n-nodes-base.itemlists/"}], "generic": []}, "alias": ["Aggregate", "<PERSON><PERSON><PERSON>", "Deduplicate", "Duplicates", "Limit", "Remove", "Slice", "Sort", "Split", "Unique", "JSON", "Transform", "Array", "List", "Object", "<PERSON><PERSON>", "Map", "Format", "Nested", "Iterate", "Summarise", "Summarize", "Group", "Pivot", "Sum", "Count", "Min", "Max"], "subcategories": {"Core Nodes": ["Helpers", "Data Transformation"]}}