{"$schema": "http://json-schema.org/draft-04/schema#", "type": "object", "properties": {"data": {"type": "object", "properties": {"startData": {"type": "object", "properties": {}}, "resultData": {"type": "object", "properties": {"runData": {"type": "object", "properties": {"Start": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce7": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Sleep 0.5 second4": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Sleep 0.5 second": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce8": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce1": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce9": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce2": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Sleep 0.5 second3": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Sleep 0.5 second1": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce10": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce3": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce11": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce4": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Sleep 0.5 second5": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Sleep 0.5 second2": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce12": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce5": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce13": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}, "Salesforce6": {"type": "array", "items": {"type": "object", "properties": {"startTime": {"type": "number"}, "executionIndex": {"type": "number"}, "source": {"type": "array", "items": {"type": "object", "properties": {"previousNode": {"type": "string"}}}}, "hints": {"type": "array", "items": {}}, "executionTime": {"type": "number"}, "executionStatus": {"type": "string"}, "data": {"type": "object", "properties": {"main": {"type": "array", "items": {"type": "array"}}}}}}}}}, "lastNodeExecuted": {"type": "string"}}}, "executionData": {"type": "object", "properties": {"contextData": {"type": "object", "properties": {}}, "nodeExecutionStack": {"type": "array", "items": {}}, "metadata": {"type": "object", "properties": {}}, "waitingExecution": {"type": "object", "properties": {}}, "waitingExecutionSource": {"type": "object", "properties": {}}}}}}, "mode": {"type": "string"}, "startedAt": {"type": "string"}, "stoppedAt": {"type": "string"}, "status": {"type": "string"}, "finished": {"type": "boolean"}}}