{"createdAt": "2021-03-03T10:18:29.938Z", "updatedAt": "2021-03-03T10:18:35.288Z", "id": "91", "name": "HTML Extract:json binary", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "51f39bec-0696-4ff7-93a8-26d105a5edfc"}, {"parameters": {"functionCode": "item.data = `<!doctype html>\n<html data-n-head-ssr>\n\n<body >\n<div data-server-rendered=\"true\" id=\"__nuxt\"><!----><div id=\"__layout\"><div class=\"layout-full-page\"><header class=\"el-header header\" style=\"height:auto;\"><!----> <div class=\"content-wrapper\"><div class=\"el-row\"><div class=\"el-col el-col-24 el-col-xs-16 el-col-sm-16 el-col-md-5 el-col-lg-6\"><a href=\"/\" aria-current=\"page\" class=\"nuxt-link-exact-active nuxt-link-active\"><svg width=\"124px\" height=\"28px\" viewBox=\"0 0 124 28\" version=\"1.1\" xmlns=\"http://www.w3.org/2000/svg\" xmlns:xlink=\"http://www.w3.org/1999/xlink\"><title>n8</title> <g id=\"nav-menu-(V1)\" stroke=\"none\" stroke-width=\"1\" fill=\"none\" fill-rule=\"evenodd\"><g id=\"nav-menu-(v1)\" transform=\"translate(-120.000000, -116.000000)\" fill-rule=\"nonzero\"><g id=\"n8\" transform=\"translate(120.000000, 116.000000)\"><path d=\"M48.7384906,0.190188679 C46.1577358,0.190188679 43.9864151,1.96792453 43.3735849,4.36113208 L35.6524528,4.36113208 C32.6226415,4.36113208 30.1581132,6.82566038 30.1581132,9.8554717 C30.1581132,11.3690566 28.9271698,12.6026415 27.4109434,12.6026415 L26.309434,12.6026415 C25.6966038,10.209434 23.5279245,8.43169811 20.9445283,8.43169811 C18.3637736,8.43169811 16.1924528,10.209434 15.5796226,12.6026415 L11.1683019,12.6026415 C10.5554717,10.209434 8.38679245,8.43169811 5.80339623,8.43169811 C2.74716981,8.43169811 0.258867925,10.9173585 0.258867925,13.9762264 C0.258867925,17.0324528 2.7445283,19.5207547 5.80339623,19.5207547 C8.38415094,19.5207547 10.5554717,17.7430189 11.1683019,15.3498113 L15.5849057,15.3498113 C16.1977358,17.7430189 18.3664151,19.5207547 20.9498113,19.5207547 C23.514717,19.5207547 25.6701887,17.769434 26.3015094,15.4 L27.4135849,15.4 C28.9271698,15.4 30.1607547,16.6309434 30.1607547,18.1471698 C30.1607547,21.1769811 32.625283,23.6415094 35.6550943,23.6415094 L37.4539623,23.6415094 C38.0667925,26.034717 40.2354717,27.8124528 42.8188679,27.8124528 C45.8750943,27.8124528 48.3633962,25.3267925 48.3633962,22.2679245 C48.3633962,19.2116981 45.8777358,16.7233962 42.8188679,16.7233962 C40.2381132,16.7233962 38.0667925,18.5011321 37.4539623,20.8943396 L35.6550943,20.8943396 C34.1415094,20.8943396 32.9079245,19.6633962 32.9079245,18.1471698 C32.9079245,16.4935849 32.1683019,15.0090566 31.0086792,14.0026415 C32.1709434,12.9935849 32.9079245,11.5116981 32.9079245,9.85811321 C32.9079245,8.3445283 34.1388679,7.1109434 35.6550943,7.1109434 L43.3762264,7.1109434 C43.9890566,9.50415094 46.1577358,11.2818868 48.7411321,11.2818868 C51.7973585,11.2818868 54.2856604,8.79622642 54.2856604,5.73735849 C54.2830189,2.67849057 51.794717,0.190188679 48.7384906,0.190188679 Z M5.80867925,16.7709434 C4.26603774,16.7709434 3.01132075,15.5162264 3.01132075,13.9735849 C3.01132075,12.4309434 4.26603774,11.1762264 5.80867925,11.1762264 C7.35132075,11.1762264 8.60603774,12.4309434 8.60603774,13.9735849 C8.60603774,15.5162264 7.35132075,16.7709434 5.80867925,16.7709434 Z M20.9498113,16.7709434 C19.4071698,16.7709434 18.1524528,15.5162264 18.1524528,13.9735849 C18.1524528,12.4309434 19.4071698,11.1762264 20.9498113,11.1762264 C22.4924528,11.1762264 23.7471698,12.4309434 23.7471698,13.9735849 C23.7471698,15.5162264 22.4924528,16.7709434 20.9498113,16.7709434 Z M42.8162264,19.4679245 C44.3588679,19.4679245 45.6135849,20.7226415 45.6135849,22.265283 C45.6135849,23.8079245 44.3588679,25.0626415 42.8162264,25.0626415 C41.2735849,25.0626415 40.0188679,23.8079245 40.0188679,22.265283 C40.0215094,20.7226415 41.2762264,19.4679245 42.8162264,19.4679245 Z M48.7384906,8.53207547 C47.1958491,8.53207547 45.9411321,7.27735849 45.9411321,5.73471698 C45.9411321,4.19207547 47.1958491,2.93735849 48.7384906,2.93735849 C50.2811321,2.93735849 51.5358491,4.19207547 51.5358491,5.73471698 C51.5358491,7.27735849 50.2811321,8.53207547 48.7384906,8.53207547 Z\" id=\"Shape\" fill=\"#FF6D5A\"></path> <g id=\"Group\" transform=\"translate(56.528302, 5.547170)\" fill=\"#384D5B\"><path d=\"M1.57962264,7.09773585 C1.57962264,6.76490566 1.40264151,6.6090566 1.0909434,6.6090566 L0.179622642,6.6090566 L0.179622642,4.76528302 L2.24792453,4.76528302 C3.20415094,4.76528302 3.67169811,5.18792453 3.67169811,6.00943396 L3.67169811,6.43207547 C3.67169811,6.78867925 3.62679245,7.07660377 3.62679245,7.07660377 L3.67169811,7.07660377 C4.1154717,6.09924528 5.44943396,4.49849057 7.8954717,4.49849057 C10.5633962,4.49849057 11.7626415,5.94339623 11.7626415,8.80943396 L11.7626415,13.6777358 C11.7626415,14.010566 11.9396226,14.1664151 12.2513208,14.1664151 L13.1626415,14.1664151 L13.1626415,16.0101887 L11.0283019,16.0101887 C10.0271698,16.0101887 9.6045283,15.5875472 9.6045283,14.5864151 L9.6045283,9.29811321 C9.6045283,7.71849057 9.29283019,6.47433962 7.49396226,6.47433962 C5.76113208,6.47433962 4.38226415,7.60754717 3.93849057,9.23207547 C3.78264151,9.67584906 3.73773585,10.1883019 3.73773585,10.7430189 L3.73773585,16.0101887 L1.58226415,16.0101887 L1.58226415,7.09773585 L1.57962264,7.09773585 Z\" id=\"Path\"></path> <path d=\"M17.6690566,7.49660377 L17.6690566,7.45169811 C17.6690566,7.45169811 15.7354717,6.42943396 15.7354717,4.25018868 C15.7354717,2.0709434 17.4683019,0.0501886792 20.6249057,0.0501886792 C23.6256604,0.0501886792 25.5381132,1.85169811 25.5381132,4.29509434 C25.5381132,6.60641509 23.649434,8.03018868 23.649434,8.03018868 L23.649434,8.07509434 C25.0732075,8.89660377 25.9845283,9.98754717 25.9845283,11.6754717 C25.9845283,14.1215094 23.7630189,16.2769811 20.5615094,16.2769811 C17.6056604,16.2769811 15.0935829,14.4332075 15.0935829,11.5196226 C15.0909434,8.94150943 17.6690566,7.49660377 17.6690566,7.49660377 Z M20.5588679,14.2535849 C22.2045283,14.2535849 23.7366038,13.165283 23.7366038,11.609434 C23.7366038,10.230566 22.5584906,9.6309434 21.0924528,9.03132075 C20.4928302,8.78566038 19.6475472,8.45283019 19.470566,8.45283019 C18.9158491,8.45283019 17.3362264,9.74188679 17.3362264,11.4086792 C17.3362264,13.165283 18.8471698,14.2535849 20.5588679,14.2535849 Z M21.7158491,7.14 C22.249434,7.14 23.3826415,5.82716981 23.3826415,4.42716981 C23.3826415,2.98226415 22.2256604,2.0709434 20.6275472,2.0709434 C18.9158491,2.0709434 17.914717,3.04830189 17.914717,4.29245283 C17.914717,5.67132075 19.0928302,6.20490566 20.4928302,6.75962264 C20.8045283,6.89698113 21.4490566,7.14 21.7158491,7.14 Z\" id=\"Shape\"></path> <path d=\"M29.405283,7.09773585 C29.405283,6.76490566 29.2283019,6.6090566 28.9166038,6.6090566 L28.005283,6.6090566 L28.005283,4.76528302 L30.0735849,4.76528302 C31.0298113,4.76528302 31.4973585,5.18792453 31.4973585,6.00943396 L31.4973585,6.43207547 C31.4973585,6.78867925 31.4524528,7.07660377 31.4524528,7.07660377 L31.4973585,7.07660377 C31.9411321,6.09924528 33.2750943,4.49849057 35.7211321,4.49849057 C38.3890566,4.49849057 39.5883019,5.94339623 39.5883019,8.80943396 L39.5883019,13.6777358 C39.5883019,14.010566 39.765283,14.1664151 40.0769811,14.1664151 L40.9883019,14.1664151 L40.9883019,16.0101887 L38.8539623,16.0101887 C37.8528302,16.0101887 37.4301887,15.5875472 37.4301887,14.5864151 L37.4301887,9.29811321 C37.4301887,7.71849057 37.1184906,6.47433962 35.3196226,6.47433962 C33.5867925,6.47433962 32.2079245,7.60754717 31.7641509,9.23207547 C31.6083019,9.67584906 31.5633962,10.1883019 31.5633962,10.7430189 L31.5633962,16.0101887 L29.4079245,16.0101887 L29.4079245,7.09773585 L29.405283,7.09773585 Z\" id=\"Path\"></path> <polygon id=\"Path\" points=\"43.54 13.72 45.7403774 13.72 45.7403774 16.0101887 43.54 16.0101887\"></polygon> <path d=\"M48.7173585,7.09773585 C48.7173585,6.76490566 48.5403774,6.6090566 48.2286792,6.6090566 L47.3173585,6.6090566 L47.3173585,4.76528302 L49.4279245,4.76528302 C50.4290566,4.76528302 50.8516981,5.18792453 50.8516981,6.1890566 L50.8516981,13.6803774 C50.8516981,14.0132075 51.0286792,14.1690566 51.3403774,14.1690566 L52.2516981,14.1690566 L52.2516981,16.0128302 L50.1411321,16.0128302 C49.14,16.0128302 48.7173585,15.5901887 48.7173585,14.5890566 L48.7173585,7.09773585 Z\" id=\"Path\"></path> <path d=\"M60.2316981,4.49584906 C63.5890566,4.49584906 66.2992453,6.96301887 66.2992453,10.365283 C66.2992453,13.7886792 63.5864151,16.2769811 60.2316981,16.2769811 C56.8743396,16.2769811 54.185283,13.7860377 54.185283,10.365283 C54.185283,6.96301887 56.8743396,4.49584906 60.2316981,4.49584906 Z M60.2316981,14.409434 C62.3660377,14.409434 64.0988679,12.7188679 64.0988679,10.3626415 C64.0988679,8.02754717 62.3660377,6.36075472 60.2316981,6.36075472 C58.1211321,6.36075472 56.3856604,8.02754717 56.3856604,10.3626415 C56.3856604,12.7215094 58.1184906,14.409434 60.2316981,14.409434 Z\" id=\"Shape\"></path></g> <path d=\"M106.230943,9.63886792 C105.124151,9.63886792 104.223396,8.73811321 104.223396,7.63132075 C104.223396,6.5245283 105.124151,5.62377358 106.230943,5.62377358 C107.337736,5.62377358 108.238491,6.5245283 108.238491,7.63132075 C108.238491,8.73811321 107.337736,9.63886792 106.230943,9.63886792 Z M106.230943,6.58792453 C105.657736,6.58792453 105.190189,7.0554717 105.190189,7.62867925 C105.190189,8.20188679 105.657736,8.66943396 106.230943,8.66943396 C106.804151,8.66943396 107.271698,8.20188679 107.271698,7.62867925 C107.271698,7.0554717 106.804151,6.58792453 106.230943,6.58792453 Z\" id=\"Shape\" fill=\"#FF6D5A\"></path></g></g></g></svg></a></div> <div class=\"hidden-sm-and-down el-col el-col-24 el-col-md-19 el-col-lg-18\"><ul role=\"menubar\" mode=\"horizontal\" class=\"default-menu el-menu--horizontal el-menu\" style=\"background-color:#fff;\"><li role=\"menuitem\" aria-haspopup=\"true\" class=\"navigation-item el-submenu\"><div class=\"el-submenu__title\" style=\"border-bottom-color:transparent;color:#333;background-color:#fff;\">Product<i class=\"el-submenu__icon-arrow el-icon-arrow-down\"></i></div><div class=\"el-menu--horizontal\" style=\"display:none;\"><ul role=\"menu\" class=\"el-menu el-menu--popup el-menu--popup-\" style=\"background-color:#fff;\"> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"color:#333;background-color:#fff;\"><a href=\"https://docs.n8n.io/getting-started/quickstart.html\" target=\"_blank\">Quickstart</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"color:#333;background-color:#fff;\"><a href=\"/integrations\">Apps &amp; nodes</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"color:#333;background-color:#fff;\"><a href=\"/workflows\">Workflows</a></li> <div class=\"dropdown-menu-divider\"></div> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"color:#333;background-color:#fff;\"><a href=\"https://docs.n8n.io/\" target=\"_blank\">Docs</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"color:#333;background-color:#fff;\"><a href=\"https://github.com/n8n-io/n8n\" target=\"_blank\">Github</a></li></ul></div></li> <li role=\"menuitem\" aria-haspopup=\"true\" class=\"navigation-item el-submenu\"><div class=\"el-submenu__title\" style=\"border-bottom-color:transparent;color:#333;background-color:#fff;\">Community<i class=\"el-submenu__icon-arrow el-icon-arrow-down\"></i></div><div class=\"el-menu--horizontal\" style=\"display:none;\"><ul role=\"menu\" class=\"el-menu el-menu--popup el-menu--popup-\" style=\"background-color:#fff;\"> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"color:#333;background-color:#fff;\"><a href=\"https://community.n8n.io\" target=\"_blank\">Forum</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"color:#333;background-color:#fff;\"><a href=\"https://medium.com/n8n-io/\" target=\"_blank\">Blog</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"color:#333;background-color:#fff;\"><a href=\"https://docs.n8n.io/reference/contributing.html\" target=\"_blank\">\n\t\t\t\tContribute\n\t\t\t</a></li></ul></div></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item navigation-item\" style=\"color:#333;border-bottom-color:transparent;background-color:#fff;\"><a href=\"https://n8n.cloud\" target=\"_blank\">n8n.cloud</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item user-menu\" style=\"color:#333;border-bottom-color:transparent;background-color:#fff;\"><a href=\"/login\" title=\"Login\"><button type=\"button\" class=\"el-button el-button--secondary el-button--small is-round\"><!----><!----><span><span>Sign in</span></span></button></a> <a href=\"/register\" title=\"Register\"><button type=\"button\" class=\"el-button el-button--primary el-button--small is-round\"><!----><!----><span><span>Register</span></span></button></a></li></ul></div> <div class=\"menu-toggle hidden-md-and-up el-col el-col-24 el-col-xs-8 el-col-sm-8\"><button type=\"button\" class=\"el-button el-button--default el-button--medium\"><!----><!----><span><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fas\" data-icon=\"bars\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" class=\"svg-inline--fa fa-bars fa-w-14\"><path fill=\"currentColor\" d=\"M16 132h416c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H16C7.163 60 0 67.163 0 76v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h416c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H16c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z\"></path></svg></span></button></div> <div class=\"hidden-md-and-up el-col el-col-24 el-col-sm-24\"><div style=\"display:none;\"><div class=\"menu-container\"><ul role=\"menubar\" mode=\"vertical\" class=\"default-menu el-menu\" style=\"background-color:#fff;\"><li role=\"menuitem\" aria-haspopup=\"true\" class=\"navigation-item el-submenu\"><div class=\"el-submenu__title\" style=\"padding-left:20px;color:#333;background-color:#fff;\">Product<i class=\"el-submenu__icon-arrow el-icon-arrow-down\"></i></div><ul role=\"menu\" class=\"el-menu el-menu--inline\" style=\"background-color:#fff;display:none;\"> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"padding-left:40px;color:#333;background-color:#fff;\"><a href=\"https://docs.n8n.io/getting-started/quickstart.html\" target=\"_blank\">Quickstart</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"padding-left:40px;color:#333;background-color:#fff;\"><a href=\"/integrations\">Apps &amp; nodes</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"padding-left:40px;color:#333;background-color:#fff;\"><a href=\"/workflows\">Workflows</a></li> <div class=\"dropdown-menu-divider\"></div> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"padding-left:40px;color:#333;background-color:#fff;\"><a href=\"https://docs.n8n.io/\" target=\"_blank\">Docs</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"padding-left:40px;color:#333;background-color:#fff;\"><a href=\"https://github.com/n8n-io/n8n\" target=\"_blank\">Github</a></li></ul></li> <li role=\"menuitem\" aria-haspopup=\"true\" class=\"navigation-item el-submenu\"><div class=\"el-submenu__title\" style=\"padding-left:20px;color:#333;background-color:#fff;\">Community<i class=\"el-submenu__icon-arrow el-icon-arrow-down\"></i></div><ul role=\"menu\" class=\"el-menu el-menu--inline\" style=\"background-color:#fff;display:none;\"> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"padding-left:40px;color:#333;background-color:#fff;\"><a href=\"https://community.n8n.io\" target=\"_blank\">Forum</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"padding-left:40px;color:#333;background-color:#fff;\"><a href=\"https://medium.com/n8n-io/\" target=\"_blank\">Blog</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item\" style=\"padding-left:40px;color:#333;background-color:#fff;\"><a href=\"https://docs.n8n.io/reference/contributing.html\" target=\"_blank\">\n\t\t\t\tContribute\n\t\t\t</a></li></ul></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item navigation-item\" style=\"padding-left:20px;color:#333;background-color:#fff;\"><a href=\"https://n8n.cloud\" target=\"_blank\">n8n.cloud</a></li> <li role=\"menuitem\" tabindex=\"-1\" class=\"el-menu-item user-menu\" style=\"padding-left:20px;color:#333;background-color:#fff;\"><a href=\"/login\" title=\"Login\"><button type=\"button\" class=\"el-button el-button--secondary el-button--small is-round\"><!----><!----><span><span>Sign in</span></span></button></a> <a href=\"/register\" title=\"Register\"><button type=\"button\" class=\"el-button el-button--primary el-button--small is-round\"><!----><!----><span><span>Register</span></span></button></a></li></ul></div></div></div></div></div></header> <div class=\"content-main\"><main class=\"el-main\"><section class=\"page-home\"><div class=\"background-image bottom-background-image\" data-v-7a372adc><img data-srcset=\"/_nuxt/img/6f75850.png 1x, /_nuxt/img/6f75850.png 1x, /_nuxt/img/1d68b2f.png 2x\" alt=\"background image\" width=\"100%\" data-v-7a372adc></div> <div class=\"section intro\"><div class=\"background-image is-mirrored\" data-v-7a372adc><img data-srcset=\"/_nuxt/img/07b40c3.png 1x, /_nuxt/img/2a0698b.png 2x\" alt=\"background image\" width=\"100%\" data-v-7a372adc></div> <div class=\"background-image\" data-v-7a372adc><img data-srcset=\"/_nuxt/img/d817727.png 1x, /_nuxt/img/cde407f.png 2x\" alt=\"background image\" width=\"100%\" data-v-7a372adc></div> <div class=\"content-wrapper\"><div class=\"mb-1 el-row\"><div class=\"el-col el-col-22 el-col-offset-1 el-col-md-offset-3 el-col-md-18\"><h1 class=\"heading center\">\n\t\t\t\t\t\tExtendable workflow automation\n\t\t\t\t\t</h1></div></div> <div class=\"mb-5 el-row\"><div class=\"el-col el-col-20 el-col-offset-2 el-col-md-offset-5 el-col-md-14\"><div class=\"sub-heading\"><a href=\"https://faircode.io\" target=\"_blank\" class=\"primary\">fair-code</a> <a href=\"https://github.com/n8n-io/n8n/blob/master/packages/cli/LICENSE.md\" title=\"License information\" target=\"_blank\">\n\t\t\t\t\t\t\tlicensed - Apache 2.0 with Commons Clause\n\t\t\t\t\t\t</a></div></div></div> <div class=\"text-center mb-8\"><button type=\"button\" class=\"el-button get-started-button el-button--primary el-button--large is-round\"><!----><!----><span>Get Started\n\t\t\t\t</span></button></div> <div class=\"badge-row el-row\" style=\"margin-left:-10px;margin-right:-10px;\"><div class=\"el-col el-col-22 el-col-offset-1 el-col-md-offset-4 el-col-md-8 el-col-lg-offset-6 el-col-lg-6\" style=\"padding-left:10px;padding-right:10px;\"><div class=\"product-hunt-badge\" data-v-ea26d5fa><a href=\"https://www.producthunt.com/posts/n8n?utm_source=badge-top-post-badge&utm_medium=badge&utm_souce=badge-n8n\" target=\"_blank\" data-v-ea26d5fa><img src=\"https://api.producthunt.com/widgets/embed-image/v1/top-post-badge.svg?post_id=170391&theme=light&period=weekly\" alt=\"n8n - Free workflow automation tool | Product Hunt Embed\" width=\"250px\" height=\"54px\" style=\"width: 250px; height: 54px;\" data-v-ea26d5fa></a></div></div> <div class=\"el-col el-col-22 el-col-offset-1 el-col-md-offset-0 el-col-md-8 el-col-lg-offset-0 el-col-lg-6\" style=\"padding-left:10px;padding-right:10px;\"><div class=\"github-badge\"><div class=\"github-badge__title\">\n\t\tGithub\n\t</div> <div class=\"gh-button-container\"><a class=\"gh-button\"><svg version=\"1.1\" width=\"12\" height=\"16\" viewBox=\"0 0 12 16\" aria-hidden=\"true\" class=\"octicon octicon-sync spin\"><path fill-rule=\"evenodd\" d=\"M10.24 7.4a4.15 4.15 0 0 1-1.2 3.6 4.346 4.346 0 0 1-5.41.54L4.8 10.4.5 9.8l.6 4.2 1.31-1.26c2.36 1.74 5.7 1.57 7.84-.54a5.876 5.876 0 0 0 1.74-4.46l-1.75-.34zM2.96 5a4.346 4.346 0 0 1 5.41-.54L7.2 5.6l4.3.6-.6-4.2-1.31 1.26c-2.36-1.74-5.7-1.57-7.85.54C.5 5.03-.06 6.65.01 8.26l1.75.35A4.17 4.17 0 0 1 2.96 5z\"></path></svg></a></div> <div class=\"gh-button-container\"><a class=\"gh-button\"><svg version=\"1.1\" width=\"12\" height=\"16\" viewBox=\"0 0 12 16\" aria-hidden=\"true\" class=\"octicon octicon-sync spin\"><path fill-rule=\"evenodd\" d=\"M10.24 7.4a4.15 4.15 0 0 1-1.2 3.6 4.346 4.346 0 0 1-5.41.54L4.8 10.4.5 9.8l.6 4.2 1.31-1.26c2.36 1.74 5.7 1.57 7.84-.54a5.876 5.876 0 0 0 1.74-4.46l-1.75-.34zM2.96 5a4.346 4.346 0 0 1 5.41-.54L7.2 5.6l4.3.6-.6-4.2-1.31 1.26c-2.36-1.74-5.7-1.57-7.85.54C.5 5.03-.06 6.65.01 8.26l1.75.35A4.17 4.17 0 0 1 2.96 5z\"></path></svg></a></div></div></div></div></div></div> <div class=\"section hero\"><svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\" preserveAspectRatio=\"none\"><defs><radialGradient id=\"grad1\" cx=\"50%\" cy=\"50%\" r=\"75%\"><stop offset=\"0%\" style=\"stop-color:rgb(253, 253, 255);stop-opacity:1;\"></stop> <stop offset=\"44%\" style=\"stop-color:rgb(253, 253, 255);stop-opacity:1;\"></stop> <stop offset=\"100%\" style=\"stop-color:rgb(237, 236, 244);stop-opacity:1;\"></stop></radialGradient></defs> <polygon fill=\"url(#grad1)\" points=\"0,0 50,15 100,0 100,100 0,100\"></polygon></svg> <div class=\"content-wrapper\"><div class=\"el-row\"><div class=\"el-col el-col-22 el-col-offset-1 el-col-md-offset-4 el-col-md-16\"><div class=\"hero-image-container\"><div class=\"hero-image-decoration left\"><img width=\"100%\"></div> <div class=\"hero-image-decoration right\"><img width=\"100%\"></div> <!----> <div class=\"hero-image-overlay\"><div class=\"hero-image-play-container\"><img src=\"/_nuxt/img/919de46.svg\" alt=\"Play icon\"></div></div></div></div> <div></div> <div class=\"text-center el-col el-col-22 el-col-offset-1 el-col-md-offset-6 el-col-md-12\"><p class=\"text muted mt-2 mb-3\">\n\t\t\t\t\t\tWatch how simple it is to design a\n\t\t\t\t\t\t<span class=\"text semibold\">Typeform</span> +\n\t\t\t\t\t\t<span class=\"text semibold\">Google Sheets</span> +\n\t\t\t\t\t\t<span class=\"text semibold\">Slack</span> workflow\n\t\t\t\t\t</p></div></div> <div class=\"mt-8 pb-5 benefits-row el-row\"><div class=\"el-col el-col-22 el-col-offset-1 el-col-md-offset-2 el-col-md-6\"><div class=\"icon-text-description-card\" data-v-a86f252e><div class=\"image-wrapper mb-3\" data-v-a86f252e><svg viewBox=\"0 0 24 24\" class=\"material-icon material-icon__primary\" style=\"width:48px;height:48px;\" data-v-1de3dd68 data-v-a86f252e><title data-v-1de3dd68>Open icon</title> <!----> <path d=\"M17.9,17.39C17.64,16.59 16.89,16 16,16H15V13A1,1 0 0,0 14,12H8V10H10A1,1 0 0,0 11,9V7H13A2,2 0 0,0 15,5V4.59C17.93,5.77 20,8.64 20,12C20,14.08 19.2,15.97 17.9,17.39M11,19.93C7.05,19.44 4,16.08 4,12C4,11.38 4.08,10.78 4.21,10.21L9,15V16A2,2 0 0,0 11,18M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2Z\" fill=\"\" data-v-1de3dd68></path></svg></div> <h3 class=\"heading\" data-v-a86f252e>Open</h3> <p class=\"text muted\" data-v-a86f252e>\n\t\t\t\t\t\tThanks to our fair-code license, run n8n locally, or the cloud.\n\t\t\t\t\t\tDecide who gets access and where your data is stored.\n\t\t\t\t\t</p></div></div> <div class=\"el-col el-col-22 el-col-offset-1 el-col-md-6\"><div class=\"icon-text-description-card\" data-v-a86f252e><div class=\"image-wrapper mb-3\" data-v-a86f252e><svg viewBox=\"0 0 24 24\" class=\"material-icon material-icon__primary\" style=\"width:48px;height:48px;\" data-v-1de3dd68 data-v-a86f252e><title data-v-1de3dd68>Extendable icon</title> <!----> <path d=\"M8,3A2,2 0 0,0 6,5V9A2,2 0 0,1 4,11H3V13H4A2,2 0 0,1 6,15V19A2,2 0 0,0 8,21H10V19H8V14A2,2 0 0,0 6,12A2,2 0 0,0 8,10V5H10V3M16,3A2,2 0 0,1 18,5V9A2,2 0 0,0 20,11H21V13H20A2,2 0 0,0 18,15V19A2,2 0 0,1 16,21H14V19H16V14A2,2 0 0,1 18,12A2,2 0 0,1 16,10V5H14V3H16Z\" fill=\"\" data-v-1de3dd68></path></svg></div> <h3 class=\"heading\" data-v-a86f252e>Extendable</h3> <p class=\"text muted\" data-v-a86f252e>\n\t\t\t\t\t\tEasy to augment with custom functions logic, and apps with minimal\n\t\t\t\t\t\tengineering effort.\n\t\t\t\t\t</p></div></div> <div class=\"el-col el-col-22 el-col-offset-1 el-col-md-6\"><div class=\"icon-text-description-card\" data-v-a86f252e><div class=\"image-wrapper mb-3\" data-v-a86f252e><svg viewBox=\"0 0 24 24\" class=\"material-icon material-icon__primary\" style=\"width:48px;height:48px;\" data-v-1de3dd68 data-v-a86f252e><title data-v-1de3dd68>Powerful icon</title> <!----> <path d=\"M12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12A10,10 0 0,0 12,2M12,4A8,8 0 0,1 20,12C20,14.4 19,16.5 17.3,18C15.9,16.7 14,16 12,16C10,16 8.2,16.7 6.7,18C5,16.5 4,14.4 4,12A8,8 0 0,1 12,4M10,6A1,1 0 0,0 9,7A1,1 0 0,0 10,8A1,1 0 0,0 11,7A1,1 0 0,0 10,6M14,6A1,1 0 0,0 13,7A1,1 0 0,0 14,8A1,1 0 0,0 15,7A1,1 0 0,0 14,6M17.09,8.94C16.96,8.94 16.84,8.97 16.7,9L13.5,10.32L13.23,10.43C12.67,10 11.91,9.88 11.25,10.15C10.23,10.56 9.73,11.73 10.15,12.75C10.56,13.77 11.73,14.27 12.75,13.85C13.41,13.59 13.88,13 14,12.28L14.23,12.18L17.45,10.88L17.47,10.87C18,10.66 18.23,10.08 18.03,9.56C17.87,9.18 17.5,8.93 17.09,8.94M7,9A1,1 0 0,0 6,10A1,1 0 0,0 7,11A1,1 0 0,0 8,10A1,1 0 0,0 7,9Z\" fill=\"\" data-v-1de3dd68></path></svg></div> <h3 class=\"heading\" data-v-a86f252e>Powerful</h3> <p class=\"text muted\" data-v-a86f252e>\n\t\t\t\t\t\tn8n's node based editor lets you map anything to everything and go\n\t\t\t\t\t\tbeyond basic ETL.\n\t\t\t\t\t</p></div></div></div></div></div> <div class=\"section features\"><div class=\"content-wrapper\"><div class=\"el-row\"><div class=\"el-col el-col-22 el-col-offset-1 el-col-md-offset-8 el-col-md-8\"><h2 class=\"heading center\">What can I do with n8n?</h2></div></div> <div class=\"features-row el-row el-row--flex\"><div class=\"el-col el-col-22 el-col-offset-1 el-col-md-offset-0 el-col-md-10\"><div class=\"pt-2\"><div description=\"Move and transform data between different apps and databases without getting caught up in API docs and troubleshooting CORS errors.\" src=\"/_nuxt/img/287318f.png\" thumbnailWidth=\"100%\" class=\"el-card feature-card is-never-shadow is-active\" data-v-911d5802><!----><div class=\"el-card__body\"><h3 class=\"heading\" data-v-911d5802>Sync data between 150+ apps</h3> <p class=\"text muted mt-2\" data-v-911d5802>\n\t\t\t\t\t\t\tMove and transform data between different apps and databases without getting caught up in API docs and troubleshooting CORS errors.\n\t\t\t\t\t\t</p></div></div><div description=\"Go beyond ETL: start with simple no-code flows and add javascript functions, conditional logic, or custom http requests in a snap.\" src=\"/_nuxt/img/2dc3ae0.gif\" thumbnailWidth=\"100%\" class=\"el-card feature-card is-never-shadow\" data-v-911d5802><!----><div class=\"el-card__body\"><h3 class=\"heading\" data-v-911d5802>Design powerful workflows</h3> <!----></div></div><div description=\"Design bespoke nodes for a proprietary CRM, endpoints for your product, or any other software, and empower your marketing and ops teams to create their own workflows.\" src=\"/_nuxt/img/42649ff.jpg\" thumbnailWidth=\"100%\" class=\"el-card feature-card is-never-shadow\" data-v-911d5802><!----><div class=\"el-card__body\"><h3 class=\"heading\" data-v-911d5802>Create custom integrations</h3> <!----></div></div></div></div> <div class=\"image-col el-col el-col-22 el-col-offset-1 el-col-md-offset-2 el-col-md-12\"><div class=\"feature-image-container\"><!----> <p class=\"text muted text-center\">\n\t\t\t\t\t\t\tSee what else you can do with n8n! Visit our\n\t\t\t\t\t\t\t<a href=\"https://medium.com/n8n-io\" target=\"_blank\">Medium publication</a></p></div></div></div></div></div> <div class=\"section get-started\"><div class=\"content-wrapper\"><div class=\"el-row\"><div class=\"el-col el-col-22 el-col-offset-1 el-col-md-offset-8 el-col-md-8\"><h2 class=\"heading center\">How to get started?</h2></div></div> <div class=\"get-started-row el-row\" style=\"margin-left:-10px;margin-right:-10px;\"><div class=\"el-col el-col-24 el-col-md-12\" style=\"padding-left:10px;padding-right:10px;\"><h3 class=\"heading center mb-2\">\n\t\t\t\t\t\tCLI\n\t\t\t\t\t</h3> <div class=\"execution-code cli-npx\"><div class=\"click-to-copy clickable\">\n\t\tClick to copy\n\t</div> <!----></div> <div class=\"execution-code cli-npm-install\"><div class=\"click-to-copy clickable\">\n\t\tClick to copy\n\t</div> <!----></div> <div class=\"text-center\"><a href=\"https://github.com/n8n-io/n8n/blob/master/packages/cli/README.md\" target=\"_blank\" class=\"link-primary\">\n\t\t\t\t\t\t\tAdditional information about CLI\n\t\t\t\t\t\t</a></div></div> <div class=\"el-col el-col-24 el-col-md-12\" style=\"padding-left:10px;padding-right:10px;\"><h3 class=\"heading center mb-2\">\n\t\t\t\t\t\tDocker\n\t\t\t\t\t</h3> <div class=\"execution-code docker\"><div class=\"click-to-copy clickable\">\n\t\tClick to copy\n\t</div> <!----></div> <div class=\"text-center\"><a href=\"https://github.com/n8n-io/n8n/blob/master/docker/images/n8n/README.md\" target=\"_blank\" class=\"link-primary\">\n\t\t\t\t\t\t\tAdditional information about docker deployment\n\t\t\t\t\t\t</a></div></div></div></div></div> <div class=\"section hosting-info\"><div class=\"content-wrapper\"><div class=\"el-row\"><div class=\"el-col el-col-22 el-col-offset-1 el-col-md-offset-5 el-col-md-14\"><div class=\"el-card hosting-info-card is-never-shadow\"><!----><div class=\"el-card__body\"><div class=\"el-row is-justify-space-between is-align-middle el-row--flex\"><span>\n\t\t\t\t\t\t\t\tn8n.cloud - our hosted offering is now in early access\n\t\t\t\t\t\t\t</span> <div><a href=\"https://n8n.cloud?utm_source=n8n.io&amp;utm_medium=web&amp;utm_campaign=homepage-cloud-cta\" target=\"_blank\"><button type=\"button\" class=\"el-button el-button--default is-round\"><!----><!----><span>Learn more</span></button></a></div></div></div></div></div></div></div></div> <div class=\"section contact-info dark pt-8 pb-8\"><div class=\"content-wrapper\"><div class=\"el-row\"><div class=\"el-col el-col-22 el-col-offset-1 el-col-md-9\"><h2 class=\"heading mb-2\">\n\t\t\t\t\t\tGet updates from n8n\n\t\t\t\t\t</h2></div></div> <div class=\"mb-5 el-row\"><div class=\"el-col el-col-22 el-col-offset-1 el-col-md-11\"><div class=\"branding-items\"><a href=\"https://medium.com/n8n-io\" class=\"branding-item\" data-v-a3c1534a><div class=\"branding-item__content\" data-v-a3c1534a><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"medium\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" class=\"svg-inline--fa fa-medium fa-w-14\" data-v-a3c1534a data-v-a3c1534a><path fill=\"currentColor\" d=\"M0 32v448h448V32H0zm372.2 106.1l-24 23c-2.1 1.6-3.1 4.2-2.7 6.7v169.3c-.4 2.6.6 5.2 2.7 6.7l23.5 23v5.1h-118V367l24.3-23.6c2.4-2.4 2.4-3.1 2.4-6.7V199.8l-67.6 171.6h-9.1L125 199.8v115c-.7 4.8 1 9.7 4.4 13.2l31.6 38.3v5.1H71.2v-5.1l31.6-38.3c3.4-3.5 4.9-8.4 4.1-13.2v-133c.4-3.7-1-7.3-3.8-9.8L75 138.1V133h87.3l67.4 148L289 133.1h83.2v5z\" data-v-a3c1534a data-v-a3c1534a></path></svg> <span data-v-a3c1534a>Blog</span></div></a><a href=\"https://twitter.com/n8n_io\" class=\"branding-item\" data-v-a3c1534a><div class=\"branding-item__content\" data-v-a3c1534a><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"twitter\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" class=\"svg-inline--fa fa-twitter fa-w-16\" data-v-a3c1534a data-v-a3c1534a><path fill=\"currentColor\" d=\"M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z\" data-v-a3c1534a data-v-a3c1534a></path></svg> <span data-v-a3c1534a>Twitter</span></div></a><a href=\"https://github.com/n8n-io/n8n\" class=\"branding-item\" data-v-a3c1534a><div class=\"branding-item__content\" data-v-a3c1534a><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"github\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 496 512\" class=\"svg-inline--fa fa-github fa-w-16\" data-v-a3c1534a data-v-a3c1534a><path fill=\"currentColor\" d=\"M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z\" data-v-a3c1534a data-v-a3c1534a></path></svg> <span data-v-a3c1534a>Github</span></div></a><a href=\"https://linkedin.com/company/n8n\" class=\"branding-item\" data-v-a3c1534a><div class=\"branding-item__content\" data-v-a3c1534a><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"linkedin\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" class=\"svg-inline--fa fa-linkedin fa-w-14\" data-v-a3c1534a data-v-a3c1534a><path fill=\"currentColor\" d=\"M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z\" data-v-a3c1534a data-v-a3c1534a></path></svg> <span data-v-a3c1534a>LinkedIn</span></div></a><a href=\"https://www.facebook.com/n8nio/\" class=\"branding-item\" data-v-a3c1534a><div class=\"branding-item__content\" data-v-a3c1534a><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"facebook\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" class=\"svg-inline--fa fa-facebook fa-w-16\" data-v-a3c1534a data-v-a3c1534a><path fill=\"currentColor\" d=\"M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z\" data-v-a3c1534a data-v-a3c1534a></path></svg> <span data-v-a3c1534a>Facebook</span></div></a><a href=\"https://www.youtube.com/c/n8n-io\" class=\"branding-item\" data-v-a3c1534a><div class=\"branding-item__content\" data-v-a3c1534a><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"youtube\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 576 512\" class=\"svg-inline--fa fa-youtube fa-w-18\" data-v-a3c1534a data-v-a3c1534a><path fill=\"currentColor\" d=\"M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z\" data-v-a3c1534a data-v-a3c1534a></path></svg> <span data-v-a3c1534a>YouTube</span></div></a></div></div></div> <div class=\"el-row\"><div class=\"el-col el-col-22 el-col-offset-1 el-col-md-9\"><div class=\"mail-chimp-subscribe-form\"><h3 class=\"heading mb-2\">Subscribe to our newsletter</h3> <div id=\"mc_embed_signup\"><form action=\"https://n8n.us20.list-manage.com/subscribe/post?u=2c8845820b0d9053a7bd0fa5f&id=72da27343d\" method=\"post\" id=\"mc-embedded-subscribe-form\" name=\"mc-embedded-subscribe-form\" target=\"_blank\" novalidate=\"novalidate\" class=\"validate\"><div class=\"mb-2 el-row\" style=\"margin-left:-5px;margin-right:-5px;\"><div class=\"el-col el-col-14 el-col-sm-16\" style=\"padding-left:5px;padding-right:5px;\"><div class=\"el-input\"><!----><input type=\"text\" autocomplete=\"off\" name=\"EMAIL\" placeholder=\"Email\" class=\"el-input__inner\"><!----><!----><!----><!----></div></div> <div class=\"el-col el-col-10 el-col-sm-8\" style=\"padding-left:5px;padding-right:5px;\"><button type=\"submit\" class=\"el-button el-button--primary is-round\"><!----><!----><span>Subscribe\n\t\t\t\t\t</span></button></div></div> <div aria-hidden=\"true\" style=\"position: absolute; left: -5000px;\"><input type=\"text\" name=\"b_2c8845820b0d9053a7bd0fa5f_72da27343d\" tabindex=\"-1\" value></div></form></div> <p class=\"text mini muted\">\n\t\tBy submitting your email address you agree to your personal data being\n\t\tstored and used to provide you with information and commercial offers\n\t\tabout n8n.\n\t</p></div></div></div></div></div></section></main></div> <footer class=\"el-footer\" style=\"height:60px;\" data-v-636fce46><div class=\"content-wrapper el-row\" data-v-636fce46><div class=\"left-side el-col el-col-24 el-col-sm-24 el-col-md-8\" data-v-636fce46>\n\t\t\tÂ© 2019-2021 n8n.io All rights Reserved\n\t\t</div> <div class=\"right-side el-col el-col-24 el-col-sm-24 el-col-md-16\" data-v-636fce46><a href=\"https://n8n.join.com\" target=\"_blank\" class=\"link-primary\" data-v-636fce46>\n\t\t\t\tJobs\n\t\t\t</a> <a href=\"https://medium.com/n8n-io/\" target=\"_blank\" class=\"link-primary\" data-v-636fce46>\n\t\t\t\tBlog\n\t\t\t</a> <a href=\"/contact\" class=\"link-primary\" data-v-636fce46>\n\t\t\t\tContact\n\t\t\t</a> <a href=\"/press\" class=\"link-primary\" data-v-636fce46>\n\t\t\t\tPress\n\t\t\t</a> <a href=\"/impressum\" class=\"link-primary\" data-v-636fce46>\n\t\t\t\tImpressum\n\t\t\t</a> <a href=\"/privacy\" class=\"link-primary\" data-v-636fce46>\n\t\t\t\tPrivacy\n\t\t\t</a> <a href=\"/terms\" class=\"link-primary\" data-v-636fce46>\n\t\t\t\tTerms of Service\n\t\t\t</a></div> <div class=\"social-media el-col el-col-24 el-col-sm-24 el-col-md-24\" data-v-636fce46>\n\t\t\tFollow us on :\n\t\t\t<a href=\"https://medium.com/n8n-io\" target=\"_blank\" data-v-636fce46><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"medium\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" class=\"svg-inline--fa fa-medium fa-w-14\" data-v-636fce46><path fill=\"currentColor\" d=\"M0 32v448h448V32H0zm372.2 106.1l-24 23c-2.1 1.6-3.1 4.2-2.7 6.7v169.3c-.4 2.6.6 5.2 2.7 6.7l23.5 23v5.1h-118V367l24.3-23.6c2.4-2.4 2.4-3.1 2.4-6.7V199.8l-67.6 171.6h-9.1L125 199.8v115c-.7 4.8 1 9.7 4.4 13.2l31.6 38.3v5.1H71.2v-5.1l31.6-38.3c3.4-3.5 4.9-8.4 4.1-13.2v-133c.4-3.7-1-7.3-3.8-9.8L75 138.1V133h87.3l67.4 148L289 133.1h83.2v5z\" data-v-636fce46></path></svg>\n\t\t\t\tn8n-io\n\t\t\t</a> <a href=\"https://github.com/n8n-io/n8n\" target=\"_blank\" data-v-636fce46><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"github\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 496 512\" class=\"svg-inline--fa fa-github fa-w-16\" data-v-636fce46><path fill=\"currentColor\" d=\"M165.9 397.4c0 2-2.3 3.6-5.2 3.6-3.3.3-5.6-1.3-5.6-3.6 0-2 2.3-3.6 5.2-3.6 3-.3 5.6 1.3 5.6 3.6zm-31.1-4.5c-.7 2 1.3 4.3 4.3 4.9 2.6 1 5.6 0 6.2-2s-1.3-4.3-4.3-5.2c-2.6-.7-5.5.3-6.2 2.3zm44.2-1.7c-2.9.7-4.9 2.6-4.6 4.9.3 2 2.9 3.3 5.9 2.6 2.9-.7 4.9-2.6 4.6-4.6-.3-1.9-3-3.2-5.9-2.9zM244.8 8C106.1 8 0 113.3 0 252c0 110.9 69.8 205.8 169.5 239.2 12.8 2.3 17.3-5.6 17.3-12.1 0-6.2-.3-40.4-.3-61.4 0 0-70 15-84.7-29.8 0 0-11.4-29.1-27.8-36.6 0 0-22.9-15.7 1.6-15.4 0 0 24.9 2 38.6 25.8 21.9 38.6 58.6 27.5 72.9 20.9 2.3-16 8.8-27.1 16-33.7-55.9-6.2-112.3-14.3-112.3-110.5 0-27.5 7.6-41.3 23.6-58.9-2.6-6.5-11.1-33.3 2.6-67.9 20.9-6.5 69 27 69 27 20-5.6 41.5-8.5 62.8-8.5s42.8 2.9 62.8 8.5c0 0 48.1-33.6 69-27 13.7 34.7 5.2 61.4 2.6 67.9 16 17.7 25.8 31.5 25.8 58.9 0 96.5-58.9 104.2-114.8 110.5 9.2 7.9 17 22.9 17 46.4 0 33.7-.3 75.4-.3 83.6 0 6.5 4.6 14.4 17.3 12.1C428.2 457.8 496 362.9 496 252 496 113.3 383.5 8 244.8 8zM97.2 352.9c-1.3 1-1 3.3.7 5.2 1.6 1.6 3.9 2.3 5.2 1 1.3-1 1-3.3-.7-5.2-1.6-1.6-3.9-2.3-5.2-1zm-10.8-8.1c-.7 1.3.3 2.9 2.3 3.9 1.6 1 3.6.7 4.3-.7.7-1.3-.3-2.9-2.3-3.9-2-.6-3.6-.3-4.3.7zm32.4 35.6c-1.6 1.3-1 4.3 1.3 6.2 2.3 2.3 5.2 2.6 6.5 1 1.3-1.3.7-4.3-1.3-6.2-2.2-2.3-5.2-2.6-6.5-1zm-11.4-14.7c-1.6 1-1.6 3.6 0 5.9 1.6 2.3 4.3 3.3 5.6 2.3 1.6-1.3 1.6-3.9 0-6.2-1.4-2.3-4-3.3-5.6-2z\" data-v-636fce46></path></svg>\n\t\t\t\tn8n-io\n\t\t\t</a> <a href=\"https://www.facebook.com/n8nio\" target=\"_blank\" data-v-636fce46><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"facebook\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" class=\"svg-inline--fa fa-facebook fa-w-16\" data-v-636fce46><path fill=\"currentColor\" d=\"M504 256C504 119 393 8 256 8S8 119 8 256c0 123.78 90.69 226.38 209.25 245V327.69h-63V256h63v-54.64c0-62.15 37-96.48 93.67-96.48 27.14 0 55.52 4.84 55.52 4.84v61h-31.28c-30.8 0-40.41 19.12-40.41 38.73V256h68.78l-11 71.69h-57.78V501C413.31 482.38 504 379.78 504 256z\" data-v-636fce46></path></svg>\n\t\t\t\tn8nio\n\t\t\t</a> <a href=\"https://twitter.com/n8n_io\" target=\"_blank\" data-v-636fce46><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"twitter\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 512 512\" class=\"svg-inline--fa fa-twitter fa-w-16\" data-v-636fce46><path fill=\"currentColor\" d=\"M459.37 151.716c.325 4.548.325 9.097.325 13.645 0 138.72-105.583 298.558-298.558 298.558-59.452 0-114.68-17.219-161.137-47.106 8.447.974 16.568 1.299 25.34 1.299 49.055 0 94.213-16.568 130.274-44.832-46.132-.975-84.792-31.188-98.112-72.772 6.498.974 12.995 1.624 19.818 1.624 9.421 0 18.843-1.3 27.614-3.573-48.081-9.747-84.143-51.98-84.143-102.985v-1.299c13.969 7.797 30.214 12.67 47.431 13.319-28.264-18.843-46.781-51.005-46.781-87.391 0-19.492 5.197-37.36 14.294-52.954 51.655 63.675 129.3 105.258 216.365 109.807-1.624-7.797-2.599-15.918-2.599-24.04 0-57.828 46.782-104.934 104.934-104.934 30.213 0 57.502 12.67 76.67 33.137 23.715-4.548 46.456-13.32 66.599-25.34-7.798 24.366-24.366 44.833-46.132 57.827 21.117-2.273 41.584-8.122 60.426-16.243-14.292 20.791-32.161 39.308-52.628 54.253z\" data-v-636fce46></path></svg>\n\t\t\t\tn8n_io\n\t\t\t</a> <a href=\"https://www.linkedin.com/company/n8n\" target=\"_blank\" data-v-636fce46><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"linkedin\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 448 512\" class=\"svg-inline--fa fa-linkedin fa-w-14\" data-v-636fce46><path fill=\"currentColor\" d=\"M416 32H31.9C14.3 32 0 46.5 0 64.3v383.4C0 465.5 14.3 480 31.9 480H416c17.6 0 32-14.5 32-32.3V64.3c0-17.8-14.4-32.3-32-32.3zM135.4 416H69V202.2h66.5V416zm-33.2-243c-21.3 0-38.5-17.3-38.5-38.5S80.9 96 102.2 96c21.2 0 38.5 17.3 38.5 38.5 0 21.3-17.2 38.5-38.5 38.5zm282.1 243h-66.4V312c0-24.8-.5-56.7-34.5-56.7-34.6 0-39.9 27-39.9 54.9V416h-66.4V202.2h63.7v29.2h.9c8.9-16.8 30.6-34.5 62.9-34.5 67.2 0 79.7 44.3 79.7 101.9V416z\" data-v-636fce46></path></svg>\n\t\t\t\tn8n\n\t\t\t</a> <a href=\"https://www.youtube.com/c/n8n-io\" target=\"_blank\" data-v-636fce46><svg aria-hidden=\"true\" focusable=\"false\" data-prefix=\"fab\" data-icon=\"youtube\" role=\"img\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 576 512\" class=\"svg-inline--fa fa-youtube fa-w-18\" data-v-636fce46><path fill=\"currentColor\" d=\"M549.655 124.083c-6.281-23.65-24.787-42.276-48.284-48.597C458.781 64 288 64 288 64S117.22 64 74.629 75.486c-23.497 6.322-42.003 24.947-48.284 48.597-11.412 42.867-11.412 132.305-11.412 132.305s0 89.438 11.412 132.305c6.281 23.65 24.787 41.5 48.284 47.821C117.22 448 288 448 288 448s170.78 0 213.371-11.486c23.497-6.321 42.003-24.171 48.284-47.821 11.412-42.867 11.412-132.305 11.412-132.305s0-89.438-11.412-132.305zm-317.51 213.508V175.185l142.739 81.205-142.739 81.201z\" data-v-636fce46></path></svg>\n\t\t\t\tn8n-io\n\t\t\t</a></div></div></footer> <iframe src=\"https://api.n8n.io/hub\" style=\"display:none;\" data-v-18ed4a81></iframe></div></div></div><script>window.__NUXT__=(function(a,b){return {layout:\"full-page\",data:[{}],fetch:[],error:b,state:{auth:{user:b,sessionExpired:a,isSetup:a},nodes:{loading:a,nodes:[]},workflows:{loading:a,workflows:[]}},serverRendered:true,routePath:\"\\u002F\"}}(false,null));</script><script src=\"/_nuxt/652dfd6f52c327d4e493.js\" defer></script><script src=\"/_nuxt/61b792615dc1aa91f2f5.js\" defer></script><script src=\"/_nuxt/dd9eaf3f2de142573263.js\" defer></script><script src=\"/_nuxt/a0a3f2a9b6017b06ceea.js\" defer></script><script src=\"/_nuxt/c598e32bb061ebf8a2a1.js\" defer></script>\n<div id=\"consent-container\"></div>\n</body>\n</html>`;\nreturn item;"}, "name": "FunctionItem", "type": "n8n-nodes-base.functionItem", "typeVersion": 1, "position": [450, 300], "notesInFlow": true, "notes": "Set html data", "id": "70ab3e66-cac8-45fd-917d-838e2538ad69"}, {"parameters": {"extractionValues": {"values": [{"key": "extracted", "cssSelector": "li>a", "returnArray": true}]}, "options": {"trimValues": true}}, "name": "HTML Extract", "type": "n8n-nodes-base.htmlExtract", "typeVersion": 1, "position": [650, 300], "id": "f27a2f9c-1946-4cac-96fe-d980fe2e5077"}, {"parameters": {"functionCode": "testData = [ \"Quickstart\", \"Apps & nodes\", \"Workflows\", \"Docs\", \"Github\", \"Forum\", \"Blog\", \"Contribute\", \"n8n.cloud\", \"Sign in\", \"Register\", \"Quickstart\", \"Apps & nodes\", \"Workflows\", \"Docs\", \"Github\", \"Forum\", \"Blog\", \"Contribute\", \"n8n.cloud\", \"Sign in\", \"Register\" ].join('');\nresultData = $node[\"HTML Extract\"].json[\"extracted\"].join('')\nif(testData !== resultData){\n  throw new Error('Error in extracting html from json data');\n}\nreturn items;"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [850, 300], "notesInFlow": true, "notes": "Verify the result", "id": "97bc0163-8d74-4f5b-b65c-ec1354333908"}, {"parameters": {"mode": "jsonToBinary", "convertAllData": false, "options": {"useRawData": true}}, "name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "typeVersion": 1, "position": [650, 500], "id": "c25511ef-8cbc-4d29-bc2f-3983d900e802"}, {"parameters": {"sourceData": "binary", "extractionValues": {"values": [{"key": "extracted", "cssSelector": "li>a", "returnArray": true}]}, "options": {"trimValues": true}}, "name": "HTML Extract1", "type": "n8n-nodes-base.htmlExtract", "typeVersion": 1, "position": [850, 500], "id": "9df0c1ed-008b-4820-958f-c87330a9000d"}, {"parameters": {"functionCode": "testData = [ \"Quickstart\", \"Apps & nodes\", \"Workflows\", \"Docs\", \"Github\", \"Forum\", \"Blog\", \"Contribute\", \"n8n.cloud\", \"Sign in\", \"Register\", \"Quickstart\", \"Apps & nodes\", \"Workflows\", \"Docs\", \"Github\", \"Forum\", \"Blog\", \"Contribute\", \"n8n.cloud\", \"Sign in\", \"Register\" ].join('');\nresultData = $node[\"HTML Extract1\"].json[\"extracted\"].join('')\nif(testData !== resultData){\n  throw new Error('Error in extracting html from binary data');\n}\nreturn items;"}, "name": "Function1", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [1050, 500], "notesInFlow": true, "notes": "Verify the result", "id": "a36ed52d-5256-4cbf-b96f-596574b092fc"}], "connections": {"FunctionItem": {"main": [[{"node": "HTML Extract", "type": "main", "index": 0}, {"node": "Move Binary Data", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "FunctionItem", "type": "main", "index": 0}]]}, "HTML Extract": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Move Binary Data": {"main": [[{"node": "HTML Extract1", "type": "main", "index": 0}]]}, "HTML Extract1": {"main": [[{"node": "Function1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}