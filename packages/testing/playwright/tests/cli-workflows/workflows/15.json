{"createdAt": "2021-02-16T10:16:46.803Z", "updatedAt": "2021-05-20T15:21:14.466Z", "id": "15", "name": "DropBox:File: upload move copy download delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [120, 300], "id": "96dbd3bb-afe7-43ce-a204-ab0a1fec38e1"}, {"parameters": {"path": "={{$json[\"metadata\"][\"path_display\"]}}/testFile", "binaryData": true}, "name": "Dropbox", "type": "n8n-nodes-base.dropbox", "typeVersion": 1, "position": [750, 300], "alwaysOutputData": true, "credentials": {"dropboxApi": {"id": "31", "name": "Dropbox creds"}}, "continueOnFail": true, "id": "ae1f6047-70fc-4da6-a602-663fc63442e4"}, {"parameters": {"operation": "move", "path": "={{$node[\"Dropbox8\"].json[\"metadata\"][\"path_display\"]}}/testFile", "toPath": "={{$node[\"Dropbox8\"].json[\"metadata\"][\"path_display\"]}}/moveTestFile{{Date.now()}}"}, "name": "Dropbox2", "type": "n8n-nodes-base.dropbox", "typeVersion": 1, "position": [1050, 300], "alwaysOutputData": true, "credentials": {"dropboxApi": {"id": "31", "name": "Dropbox creds"}}, "id": "6b0ee1ef-fd36-4a78-bf5a-77a6351e7cb5"}, {"parameters": {"operation": "copy", "path": "={{$json[\"metadata\"][\"path_display\"]}}", "toPath": "={{$node[\"Dropbox8\"].json[\"metadata\"][\"path_display\"]}}/copiedTestFile"}, "name": "Dropbox3", "type": "n8n-nodes-base.dropbox", "typeVersion": 1, "position": [1210, 300], "alwaysOutputData": true, "credentials": {"dropboxApi": {"id": "31", "name": "Dropbox creds"}}, "id": "249d895c-adab-4ef9-a9de-107a707eedc2"}, {"parameters": {"operation": "delete", "path": "={{$json[\"metadata\"][\"path_display\"]}}"}, "name": "Dropbox4", "type": "n8n-nodes-base.dropbox", "typeVersion": 1, "position": [1490, 300], "alwaysOutputData": true, "credentials": {"dropboxApi": {"id": "31", "name": "Dropbox creds"}}, "id": "6f5575aa-9fb2-4ce9-8d17-0955797fb62d"}, {"parameters": {"operation": "download", "path": "={{$json[\"metadata\"][\"path_display\"]}}"}, "name": "Dropbox5", "type": "n8n-nodes-base.dropbox", "typeVersion": 1, "position": [1210, 450], "alwaysOutputData": true, "credentials": {"dropboxApi": {"id": "31", "name": "Dropbox creds"}}, "id": "b982811b-6408-4410-8519-fa3a1df215ba"}, {"parameters": {"operation": "delete", "path": "={{$json[\"metadata\"][\"path_display\"]}}"}, "name": "Dropbox6", "type": "n8n-nodes-base.dropbox", "typeVersion": 1, "position": [1500, 450], "alwaysOutputData": true, "credentials": {"dropboxApi": {"id": "31", "name": "Dropbox creds"}}, "id": "e168f6df-3d70-4256-a19d-ada81d6e29d1"}, {"parameters": {"filePath": "/tmp/n8n-logo.png"}, "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [450, 300], "id": "0ac33c86-3813-4af0-be35-9201501cbd28"}, {"parameters": {"resource": "folder", "path": "=/testFolder{{Date.now()}}"}, "name": "Dropbox8", "type": "n8n-nodes-base.dropbox", "typeVersion": 1, "position": [300, 130], "credentials": {"dropboxApi": {"id": "31", "name": "Dropbox creds"}}, "id": "8d123a27-69dc-4fb1-95b4-c5d5eb90e07b"}, {"parameters": {"resource": "folder", "operation": "list", "path": "={{$node[\"Dropbox10\"].json[\"metadata\"][\"path_display\"]}}", "limit": 1, "filters": {"include_deleted": true}}, "name": "Dropbox9", "type": "n8n-nodes-base.dropbox", "typeVersion": 1, "position": [2070, 150], "alwaysOutputData": true, "credentials": {"dropboxApi": {"id": "31", "name": "Dropbox creds"}}, "id": "09395798-ba6f-4d77-a96a-01b162e9695f"}, {"parameters": {"resource": "folder", "operation": "move", "path": "={{$node[\"Dropbox8\"].json[\"metadata\"][\"path_display\"]}}", "toPath": "={{$node[\"Dropbox8\"].json[\"metadata\"][\"path_display\"]}}Renamed"}, "name": "Dropbox10", "type": "n8n-nodes-base.dropbox", "typeVersion": 1, "position": [1800, 150], "credentials": {"dropboxApi": {"id": "31", "name": "Dropbox creds"}}, "id": "7eca30c5-506c-46cc-bbe0-6ded1099dee3"}, {"parameters": {"resource": "folder", "operation": "delete", "path": "={{$node[\"Dropbox10\"].json[\"metadata\"][\"path_display\"]}}"}, "name": "Dropbox11", "type": "n8n-nodes-base.dropbox", "typeVersion": 1, "position": [2380, 150], "credentials": {"dropboxApi": {"id": "31", "name": "Dropbox creds"}}, "id": "f513eda7-84d7-4903-ad94-c33c6f1a37a9"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.8 seconds", "type": "n8n-nodes-base.function", "position": [600, 300], "typeVersion": 1, "id": "28826f4e-1aeb-4bad-9516-fd49a29a4db5"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.8 seconds1", "type": "n8n-nodes-base.function", "position": [900, 300], "typeVersion": 1, "id": "55fa0728-bd74-41b0-b0b4-6b59175633b8"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.8 seconds2", "type": "n8n-nodes-base.function", "position": [1350, 300], "typeVersion": 1, "id": "694407f2-2225-459d-bfd3-23f9371c7e22"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.8 seconds3", "type": "n8n-nodes-base.function", "position": [1350, 450], "typeVersion": 1, "id": "c42d5236-994b-446a-9427-2095bb3b5121"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.8 seconds4", "type": "n8n-nodes-base.function", "position": [2230, 150], "typeVersion": 1, "id": "84d5255b-7ef5-4f96-92dc-4bd7d406cba7"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.8 seconds5", "type": "n8n-nodes-base.function", "position": [1930, 150], "typeVersion": 1, "id": "425dc02d-8d3c-42f9-a812-3a223f6b7a47"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.8 seconds6", "type": "n8n-nodes-base.function", "position": [1650, 450], "typeVersion": 1, "id": "de860b61-34ef-4faa-a0a8-6054fcd8079b"}], "connections": {"Start": {"main": [[{"node": "Dropbox8", "type": "main", "index": 0}]]}, "Dropbox": {"main": [[{"node": "Sleep 0.8 seconds1", "type": "main", "index": 0}]]}, "Dropbox2": {"main": [[{"node": "Dropbox3", "type": "main", "index": 0}, {"node": "Dropbox5", "type": "main", "index": 0}]]}, "Dropbox3": {"main": [[{"node": "Sleep 0.8 seconds2", "type": "main", "index": 0}]]}, "Dropbox5": {"main": [[{"node": "Sleep 0.8 seconds3", "type": "main", "index": 0}]]}, "Dropbox6": {"main": [[{"node": "Sleep 0.8 seconds6", "type": "main", "index": 0}]]}, "Read Binary File": {"main": [[{"node": "Sleep 0.8 seconds", "type": "main", "index": 0}]]}, "Dropbox8": {"main": [[{"node": "Read Binary File", "type": "main", "index": 0}]]}, "Dropbox9": {"main": [[{"node": "Sleep 0.8 seconds4", "type": "main", "index": 0}]]}, "Dropbox10": {"main": [[{"node": "Sleep 0.8 seconds5", "type": "main", "index": 0}]]}, "Sleep 0.8 seconds": {"main": [[{"node": "Dropbox", "type": "main", "index": 0}]]}, "Sleep 0.8 seconds1": {"main": [[{"node": "Dropbox2", "type": "main", "index": 0}]]}, "Sleep 0.8 seconds2": {"main": [[{"node": "Dropbox4", "type": "main", "index": 0}]]}, "Sleep 0.8 seconds3": {"main": [[{"node": "Dropbox6", "type": "main", "index": 0}]]}, "Sleep 0.8 seconds4": {"main": [[{"node": "Dropbox11", "type": "main", "index": 0}]]}, "Sleep 0.8 seconds5": {"main": [[{"node": "Dropbox9", "type": "main", "index": 0}]]}, "Sleep 0.8 seconds6": {"main": [[{"node": "Dropbox10", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}