{"createdAt": "2021-03-04T09:49:58.859Z", "updatedAt": "2021-03-04T09:49:58.859Z", "id": "101", "name": "Rename<PERSON><PERSON><PERSON>", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "38b2e8c3-ddfe-4ef3-85d0-ddab8e8732db"}, {"parameters": {"keys": {"key": [{"currentKey": "toBeRenamed", "newKey": "<PERSON>amed"}]}}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.rename<PERSON><PERSON>s", "typeVersion": 1, "position": [650, 300], "id": "d4b5cdcd-3bd7-46aa-ab34-f986411cfc43"}, {"parameters": {"values": {"string": [{"name": "name", "value": "test"}, {"name": "toBeRenamed", "value": "name"}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [450, 300], "id": "0c816bc3-19b1-41f2-ac7a-ea5f2448bac2"}, {"parameters": {"functionCode": "testData = JSON.stringify(\n{\nname: \"test\",\nRenamed: \"name\"\n}\n)\nif(JSON.stringify($node['Rename Keys'].json) !== testData){\n  throw new Error('Error in rename keys node');\n}\n\nreturn items;"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [850, 300], "id": "95ab4a10-c3f3-4bad-8792-4a8dab012672"}], "connections": {"Rename Keys": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}