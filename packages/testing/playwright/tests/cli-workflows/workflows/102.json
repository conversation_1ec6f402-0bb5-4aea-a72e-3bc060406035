{"createdAt": "2021-03-04T10:41:43.011Z", "updatedAt": "2021-03-04T10:41:43.011Z", "id": "102", "name": "ReadBinaryFile", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "516a8267-1d35-4ec7-977b-87d0f961fe53"}, {"parameters": {"filePath": "/tmp/n8n-logo.png"}, "name": "Read Binary File", "type": "n8n-nodes-base.readBinaryFile", "typeVersion": 1, "position": [500, 300], "notesInFlow": true, "notes": "Read the assets/n8n-logo.png", "id": "839c17af-2415-41c4-bf13-8815df27ca4e"}, {"parameters": {"functionCode": "testData='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';\nif(items[0].binary.data.data !== testData){\n  throw new Error('Error in Read Binary File node');\n}\nreturn items;"}, "name": "Function", "type": "n8n-nodes-base.function", "typeVersion": 1, "position": [650, 300], "notesInFlow": true, "notes": "assert the base64 value", "id": "409eca37-f7a2-4ddc-9ae9-c4a9daeef481"}], "connections": {"Read Binary File": {"main": [[{"node": "Function", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Read Binary File", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}