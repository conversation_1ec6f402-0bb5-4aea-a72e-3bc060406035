{"createdAt": "2021-03-11T14:51:32.026Z", "updatedAt": "2021-03-18T10:28:03.209Z", "id": "126", "name": "Kafka", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "76f28c5d-1894-4d95-ab23-4e6e39bf69e9"}, {"parameters": {"topic": "=TopicTest{{Date.now()}}", "headersUi": {"headerValues": []}, "options": {"compression": true}}, "name": "Kafka", "type": "n8n-nodes-base.kafka", "typeVersion": 1, "position": [710, 300], "credentials": {"kafka": {"id": "93", "name": "Kafka creds"}}, "id": "9d37ab21-f676-4e88-8d71-3033d301ed62"}, {"parameters": {"values": {"string": [{"name": "input", "value": "={{(new Date()).toISOString()}}"}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [500, 300], "id": "2d1bf06b-6d8e-4f82-b56f-7fde44b74007"}], "connections": {"Set": {"main": [[{"node": "Kafka", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Set", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}