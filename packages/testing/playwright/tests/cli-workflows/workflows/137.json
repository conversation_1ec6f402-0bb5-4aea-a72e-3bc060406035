{"createdAt": "2021-03-16T15:47:37.279Z", "updatedAt": "2021-05-21T09:19:02.974Z", "id": "137", "name": "TheHive[v3]:Alert:create update get getAll promote merge:Case:create update get getAll:Observable:create update get search getAll:Task:create update get search getAll:Log:create get getAll", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 400], "id": "ade44fe5-e6a3-407d-9a19-d236c09d053e"}, {"parameters": {"title": "=Title{{Date.now()}}", "description": "desc", "date": "={{(new Date()).toISOString()}}", "tags": "test", "type": "test", "source": "n8n", "sourceRef": "={{Date.now().toString()}}", "additionalFields": {}}, "name": "TheHive", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [450, 300], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "7a175f98-ba4d-4e86-8290-1961286069a3"}, {"parameters": {"operation": "update", "id": "={{$node[\"TheHive\"].json[\"id\"]}}", "updateFields": {"tlp": 1}}, "name": "TheHive1", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [750, 300], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "07153eab-082e-4c15-a3a8-0352ce78d39a"}, {"parameters": {"operation": "get", "id": "={{$node[\"TheHive\"].json[\"id\"]}}"}, "name": "TheHive2", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [900, 300], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "55e955b5-08e5-45ac-a3e9-42a2791e0e86"}, {"parameters": {"operation": "getAll", "limit": 1, "options": {}, "filters": {}}, "name": "TheHive3", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1180, 300], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "84a55d17-c067-4d5c-b199-378ef1a5e37c"}, {"parameters": {"operation": "promote", "id": "={{$node[\"TheHive\"].json[\"id\"]}}", "additionalFields": {}}, "name": "TheHive4", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1330, 300], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "0ca31228-af2d-451f-9c65-a7c8e910639d"}, {"parameters": {"operation": "merge", "id": "={{$node[\"TheHive30\"].json[\"id\"]}}", "caseId": "={{$node[\"TheHive29\"].json[\"id\"]}}"}, "name": "TheHive5", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [940, 150], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "d11d3928-e84f-4a7b-bee0-01a6f7903741"}, {"parameters": {"resource": "case", "operation": "create", "title": "=Title{{Date.now()}}", "description": "desc", "startDate": "={{(new Date()).toISOString()}}", "owner": "nodeqa", "tags": "test", "options": {}}, "name": "TheHive6", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [450, 600], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "cf77cd22-62ec-461f-8685-35120833d1d8"}, {"parameters": {"resource": "case", "operation": "update", "id": "={{$node[\"TheHive6\"].json[\"caseId\"]}}", "updateFields": {"tlp": 3}}, "name": "TheHive7", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [600, 600], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "1fceb11e-94ea-42b2-95d0-30e9357a8aa0"}, {"parameters": {"resource": "case", "operation": "get", "id": "={{$node[\"TheHive6\"].json[\"caseId\"]}}"}, "name": "TheHive8", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [910, 600], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "52c085ae-49c1-49b9-9be5-1350d065868c"}, {"parameters": {"resource": "case", "limit": 1, "options": {}, "filters": {}}, "name": "TheHive9", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1060, 600], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "ecd0b218-41b2-4e43-9553-b17da8942a16"}, {"parameters": {"resource": "case", "operation": "executeResponder", "id": "={{$node[\"TheHive6\"].json[\"id\"]}}", "responder": "23bc4aef9aa1c88d6624004a3d04aeae"}, "name": "TheHive10", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1210, 600], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "4520dc3b-153c-4239-8e6b-814dc39a324a"}, {"parameters": {"resource": "task", "operation": "create", "caseId": "={{$node[\"TheHive6\"].json[\"caseId\"]}}", "title": "=Task{{Date.now()}}", "options": {}}, "name": "TheHive11", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [600, 770], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "c0efb989-b9b1-4cf6-84f5-d30e565eb05b"}, {"parameters": {"resource": "task", "operation": "update", "id": "={{$node[\"TheHive11\"].json[\"id\"]}}", "updateFields": {"status": "InProgress"}}, "name": "TheHive12", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [750, 770], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "79a320c7-796b-4380-bbfd-af5ef73c308c"}, {"parameters": {"resource": "task", "operation": "get", "id": "={{$node[\"TheHive11\"].json[\"id\"]}}"}, "name": "TheHive13", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1050, 770], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "7c5e1c7c-a9a2-4f67-b1d0-62ab12ea4ce9"}, {"parameters": {"resource": "task", "operation": "search", "limit": 1, "options": {}, "filters": {}}, "name": "TheHive14", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1370, 770], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "a5b3618e-5c12-480c-975d-cdf58f33e121"}, {"parameters": {"resource": "log", "operation": "create", "taskId": "={{$node[\"TheHive11\"].json[\"id\"]}}", "message": "=Message{{Date.now()}}", "startDate": "={{(new Date()).toISOString()}}", "status": "Ok", "options": {}}, "name": "TheHive16", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [750, 930], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "efeb839b-df7f-465d-9776-ad3699b8cb56"}, {"parameters": {"resource": "log", "operation": "get", "id": "={{$node[\"TheHive16\"].json[\"_id\"]}}"}, "name": "TheHive17", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1050, 930], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "df7967a1-012c-4e20-b5e4-63fa37265bf5"}, {"parameters": {"resource": "log", "taskId": "={{$node[\"TheHive16\"].json[\"_id\"]}}", "limit": 1}, "name": "TheHive18", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1200, 930], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "549b23ec-1ccb-4585-b270-b2341b8b409f"}, {"parameters": {"resource": "observable", "operation": "create", "caseId": "={{$node[\"TheHive6\"].json[\"caseId\"]}}", "dataType": "ip", "data": "**************", "message": "test", "startDate": "={{(new Date()).toISOString()}}", "ioc": true, "status": "Ok", "options": {}}, "name": "TheHive19", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [600, 450], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "86f1d7a0-f22e-46f7-b315-8aecbe4bd5dd"}, {"parameters": {"resource": "observable", "operation": "update", "id": "={{$node[\"TheHive19\"].json[\"_id\"]}}", "updateFields": {"ioc": false}}, "name": "TheHive20", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [910, 450], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "2347e027-4531-48ae-94cb-bd8dd109da0a"}, {"parameters": {"resource": "observable", "operation": "get", "id": "={{$node[\"TheHive19\"].json[\"_id\"]}}"}, "name": "TheHive21", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1060, 450], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "e12c0ed2-ea18-4a0e-a3b1-00d2e417b025"}, {"parameters": {"resource": "observable", "operation": "search", "limit": 1, "options": {}, "filters": {}}, "name": "TheHive22", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1360, 450], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "bbed23cc-38f3-4912-a6b3-bdb3e0da4932"}, {"parameters": {"resource": "observable", "caseId": "={{$node[\"TheHive6\"].json[\"_id\"]}}", "limit": 1, "options": {}}, "name": "TheHive23", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1540, 450], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "b8f259fc-1031-49ea-86b6-cd89fe12f939"}, {"parameters": {"operation": "executeResponder", "id": "={{$node[\"TheHive\"].json[\"id\"]}}", "responder": "23bc4aef9aa1c88d6624004a3d04aeae"}, "name": "TheHive15", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1510, 300], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "50e0654a-f844-43d6-9b5b-eda48c2707f4"}, {"parameters": {"resource": "observable", "operation": "executeAnalyzer", "id": "={{$node[\"TheHive21\"].json[\"id\"]}}", "dataType": "={{$node[\"TheHive21\"].json[\"dataType\"]}}", "analyzers": ["6fdd3c9b5432f1e2094cd3b8f2347d09::cortex"]}, "name": "TheHive24", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1720, 450], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "fe15f804-236e-4dd1-9e3c-13758a9ce53e"}, {"parameters": {"resource": "observable", "operation": "executeResponder", "id": "={{$node[\"TheHive21\"].json[\"id\"]}}", "responder": "fbe415a38eb649eb7df174aa11a32cfe"}, "name": "TheHive25", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1880, 450], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "9a964882-99d9-4c1e-a23b-1c45d788a9d7"}, {"parameters": {"resource": "task", "caseId": "={{$node[\"TheHive6\"].json[\"_id\"]}}", "limit": 1, "options": {}}, "name": "TheHive26", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1520, 770], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "67594eb3-fb0c-41c0-a972-b8f79585d9dc"}, {"parameters": {"resource": "task", "operation": "executeResponder", "id": "={{$node[\"TheHive11\"].json[\"id\"]}}", "responder": "23bc4aef9aa1c88d6624004a3d04aeae"}, "name": "TheHive27", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1690, 770], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "80a817e5-4922-4dea-baa6-ebb412b06178"}, {"parameters": {"resource": "log", "operation": "executeResponder", "id": "={{$node[\"TheHive16\"].json[\"_id\"]}}"}, "name": "TheHive28", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [1350, 930], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "disabled": true, "id": "90f4e4a2-0aee-4b1e-ab19-6d3136c7db7b"}, {"parameters": {"resource": "case", "operation": "create", "title": "=MergingCase{{Date.now()}}", "description": "desc", "startDate": "={{(new Date()).toISOString()}}", "owner": "nodeqa", "tags": "test", "options": {}}, "name": "TheHive29", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [450, 150], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "3c2306f0-34e0-40c8-951c-64dd9482cc22"}, {"parameters": {"title": "=MergingAlert{{Date.now()}}", "description": "desc", "date": "={{(new Date()).toISOString()}}", "tags": "test", "type": "test", "source": "n8n", "sourceRef": "={{Date.now().toString()}}", "additionalFields": {}}, "name": "TheHive30", "type": "n8n-nodes-base.theHive", "typeVersion": 1, "position": [790, 150], "credentials": {"theHiveApi": {"id": "107", "name": "The Hive API creds"}}, "id": "66b2590c-ee6e-451f-b0b6-22200d73209f"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second", "type": "n8n-nodes-base.function", "position": [610, 150], "typeVersion": 1, "id": "1a5a6ace-fef4-4fbc-b374-492dfa5ee9c9"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second1", "type": "n8n-nodes-base.function", "position": [610, 300], "typeVersion": 1, "id": "1d31e404-48c8-45b1-963a-29689132bf18"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second2", "type": "n8n-nodes-base.function", "position": [1060, 300], "typeVersion": 1, "id": "fd1d5353-73bb-4563-8f57-078ace5b7424"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second3", "type": "n8n-nodes-base.function", "position": [760, 450], "typeVersion": 1, "id": "979b0aab-acd8-4aeb-a2d5-84516d01e436"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second4", "type": "n8n-nodes-base.function", "position": [1210, 450], "typeVersion": 1, "id": "a2448c15-76d1-4611-96c1-382b8537d8e2"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second5", "type": "n8n-nodes-base.function", "position": [750, 600], "typeVersion": 1, "id": "07ec5305-28c5-4f70-a4dd-81d9222131b5"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second6", "type": "n8n-nodes-base.function", "position": [900, 770], "typeVersion": 1, "id": "6a220308-b57c-4a29-a103-f828564c75f5"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second7", "type": "n8n-nodes-base.function", "position": [900, 930], "typeVersion": 1, "id": "3d8ce87e-fa7f-4c91-9a91-84572a4cb5b9"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second8", "type": "n8n-nodes-base.function", "position": [1200, 770], "typeVersion": 1, "id": "8437d645-7789-4f5a-a2c0-217538297ead"}], "connections": {"TheHive": {"main": [[{"node": "Sleep 0.5 second1", "type": "main", "index": 0}]]}, "TheHive1": {"main": [[{"node": "TheHive2", "type": "main", "index": 0}]]}, "TheHive2": {"main": [[{"node": "TheHive15", "type": "main", "index": 0}, {"node": "Sleep 0.5 second2", "type": "main", "index": 0}]]}, "TheHive3": {"main": [[{"node": "TheHive4", "type": "main", "index": 0}]]}, "TheHive6": {"main": [[{"node": "TheHive7", "type": "main", "index": 0}, {"node": "TheHive11", "type": "main", "index": 0}, {"node": "TheHive19", "type": "main", "index": 0}]]}, "TheHive7": {"main": [[{"node": "Sleep 0.5 second5", "type": "main", "index": 0}]]}, "TheHive8": {"main": [[{"node": "TheHive9", "type": "main", "index": 0}, {"node": "TheHive10", "type": "main", "index": 0}]]}, "TheHive11": {"main": [[{"node": "TheHive12", "type": "main", "index": 0}, {"node": "TheHive16", "type": "main", "index": 0}]]}, "TheHive12": {"main": [[{"node": "Sleep 0.5 second6", "type": "main", "index": 0}]]}, "TheHive13": {"main": [[{"node": "TheHive27", "type": "main", "index": 0}, {"node": "Sleep 0.5 second8", "type": "main", "index": 0}]]}, "TheHive16": {"main": [[{"node": "Sleep 0.5 second7", "type": "main", "index": 0}]]}, "TheHive17": {"main": [[{"node": "TheHive18", "type": "main", "index": 0}, {"node": "TheHive28", "type": "main", "index": 0}]]}, "TheHive19": {"main": [[{"node": "Sleep 0.5 second3", "type": "main", "index": 0}]]}, "TheHive20": {"main": [[{"node": "TheHive21", "type": "main", "index": 0}]]}, "TheHive21": {"main": [[{"node": "TheHive24", "type": "main", "index": 0}, {"node": "TheHive25", "type": "main", "index": 0}, {"node": "Sleep 0.5 second4", "type": "main", "index": 0}]]}, "TheHive22": {"main": [[{"node": "TheHive23", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "TheHive6", "type": "main", "index": 0}, {"node": "TheHive", "type": "main", "index": 0}, {"node": "TheHive29", "type": "main", "index": 0}]]}, "TheHive14": {"main": [[{"node": "TheHive26", "type": "main", "index": 0}]]}, "TheHive30": {"main": [[{"node": "TheHive5", "type": "main", "index": 0}]]}, "TheHive29": {"main": [[{"node": "Sleep 0.5 second", "type": "main", "index": 0}]]}, "Sleep 0.5 second": {"main": [[{"node": "TheHive30", "type": "main", "index": 0}]]}, "Sleep 0.5 second1": {"main": [[{"node": "TheHive1", "type": "main", "index": 0}]]}, "Sleep 0.5 second2": {"main": [[{"node": "TheHive3", "type": "main", "index": 0}]]}, "Sleep 0.5 second3": {"main": [[{"node": "TheHive20", "type": "main", "index": 0}]]}, "Sleep 0.5 second4": {"main": [[{"node": "TheHive22", "type": "main", "index": 0}]]}, "Sleep 0.5 second5": {"main": [[{"node": "TheHive8", "type": "main", "index": 0}]]}, "Sleep 0.5 second6": {"main": [[{"node": "TheHive13", "type": "main", "index": 0}]]}, "Sleep 0.5 second7": {"main": [[{"node": "TheHive17", "type": "main", "index": 0}]]}, "Sleep 0.5 second8": {"main": [[{"node": "TheHive14", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}