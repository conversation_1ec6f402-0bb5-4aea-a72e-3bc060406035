{"createdAt": "2021-02-25T08:31:03.195Z", "updatedAt": "2021-02-25T08:32:00.908Z", "id": "62", "name": "ProfitWell:Company:getSetting:Metric:getMontly getDaily", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [300, 350], "id": "4dbeace0-2f7c-4987-b559-b94bf635956b"}, {"parameters": {"resource": "company"}, "name": "ProfitWell", "type": "n8n-nodes-base.profitWell", "typeVersion": 1, "position": [500, 270], "credentials": {"profitWellApi": {"id": "51", "name": "ProfitWell creds"}}, "id": "641ae41a-7ffd-4644-af38-336ae09074eb"}, {"parameters": {"type": "monthly", "options": {}}, "name": "ProfitWell1", "type": "n8n-nodes-base.profitWell", "typeVersion": 1, "position": [500, 420], "credentials": {"profitWellApi": {"id": "51", "name": "ProfitWell creds"}}, "id": "fa1e6b3f-6aee-43da-a1a5-7a5f5e820430"}, {"parameters": {"type": "daily", "month": "={{(new Date()).toISOString().slice(0,7)}}", "simple": false, "options": {}}, "name": "ProfitWell2", "type": "n8n-nodes-base.profitWell", "typeVersion": 1, "position": [650, 420], "credentials": {"profitWellApi": {"id": "51", "name": "ProfitWell creds"}}, "id": "fba5b0a1-1e81-473c-9973-2d7f21a646d3"}], "connections": {"Start": {"main": [[{"node": "ProfitWell", "type": "main", "index": 0}, {"node": "ProfitWell1", "type": "main", "index": 0}]]}, "ProfitWell1": {"main": [[{"node": "ProfitWell2", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}