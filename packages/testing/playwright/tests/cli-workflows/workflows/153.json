{"createdAt": "2021-03-25T09:44:00.500Z", "updatedAt": "2021-03-25T09:44:00.500Z", "id": "153", "name": "AWSLambda:invoke", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "0e6c4d13-c106-4581-890c-ff9d76f686b5"}, {"parameters": {"function": "arn:aws:lambda:us-east-1:589875339869:function:test-helloworld"}, "name": "AWS Lambda", "type": "n8n-nodes-base.awsLambda", "typeVersion": 1, "position": [450, 200], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "2f7188cc-0b10-48aa-93a7-68814c18cdcb"}, {"parameters": {"function": "arn:aws:lambda:us-east-1:589875339869:function:test-helloworld", "payload": "{\n  \"name\": \"nodeqa\"\n}"}, "name": "AWS Lambda1", "type": "n8n-nodes-base.awsLambda", "typeVersion": 1, "position": [450, 360], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "798f32bd-9032-4fad-9b3f-5cd4fb35fdb2"}], "connections": {"Start": {"main": [[{"node": "AWS Lambda", "type": "main", "index": 0}, {"node": "AWS Lambda1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}