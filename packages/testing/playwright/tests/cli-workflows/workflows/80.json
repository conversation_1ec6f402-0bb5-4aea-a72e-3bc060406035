{"createdAt": "2021-02-26T16:19:59.941Z", "updatedAt": "2021-02-26T16:20:47.874Z", "id": "80", "name": "Zendesk:Ticket:create update get getAll delete:TicketField:getAll get:User:create update getAll search get delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [200, 460], "id": "4b8759d6-5b80-4936-93a5-b13fb5796186"}, {"parameters": {"authentication": "oAuth2", "description": "=Description{{Date.now()}}", "additionalFields": {}}, "name": "Zendesk", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [400, 300], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "d19f346d-1951-48ac-a774-6816c8547a9d"}, {"parameters": {"authentication": "oAuth2", "operation": "update", "id": "={{$node[\"Zendesk\"].json[\"id\"]}}", "updateFields": {"subject": "=UpdatedSubject{{Date.now()}}"}}, "name": "Zendesk1", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [550, 300], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "f5ff3626-892b-44ad-8eda-f5c753520a17"}, {"parameters": {"authentication": "oAuth2", "operation": "get", "id": "={{$node[\"Zendesk\"].json[\"id\"]}}"}, "name": "Zendesk2", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [700, 300], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "6a72a337-f588-4c4b-844f-f96b68b2c464"}, {"parameters": {"authentication": "oAuth2", "operation": "getAll", "limit": 1, "options": {}}, "name": "Zendesk3", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [850, 300], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "a9590bd0-c6da-4d3a-9c8d-c86c00924373"}, {"parameters": {"authentication": "oAuth2", "operation": "delete", "id": "={{$node[\"Zendesk\"].json[\"id\"]}}"}, "name": "Zendesk4", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [1000, 300], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "0ea03844-4270-4aad-a614-e60d2008e773"}, {"parameters": {"authentication": "oAuth2", "resource": "ticketField", "operation": "getAll", "limit": 1}, "name": "Zendesk5", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [400, 450], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "714ab216-9da0-496d-acfa-3c82f16a22f4"}, {"parameters": {"authentication": "oAuth2", "resource": "ticketField", "ticketFieldId": "={{$node[\"Zendesk5\"].json[\"id\"]}}"}, "name": "Zendesk6", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [550, 450], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "8ee850ce-35e2-435b-8abb-e2a8820362b3"}, {"parameters": {"authentication": "oAuth2", "resource": "user", "name": "=Name{{Date.now()}}", "additionalFields": {}}, "name": "Zendesk7", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [400, 600], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "999a937f-cb90-448f-abe1-49efa8b5c6e4"}, {"parameters": {"authentication": "oAuth2", "resource": "user", "operation": "update", "id": "={{$node[\"Zendesk7\"].json[\"id\"]}}", "updateFields": {"name": "=UpdatedName{{Date.now()}}"}}, "name": "Zendesk8", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [550, 600], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "3ca61e62-ea9e-4431-b099-e6185c7546bb"}, {"parameters": {"authentication": "oAuth2", "resource": "user", "operation": "getAll", "limit": 1, "filters": {}}, "name": "Zendesk9", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [700, 600], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "6ab5a5c4-715e-4db4-9e60-44ffc3d7858b"}, {"parameters": {"authentication": "oAuth2", "resource": "user", "operation": "search", "limit": 1, "filters": {}}, "name": "Zendesk10", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [850, 600], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "1c032158-0872-4518-b7d9-c41cd7402baf"}, {"parameters": {"authentication": "oAuth2", "resource": "user", "operation": "get", "id": "={{$node[\"Zendesk7\"].json[\"id\"]}}"}, "name": "Zendesk11", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [1000, 600], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "5b713747-e0ff-4918-bfa9-daac79899b90"}, {"parameters": {"authentication": "oAuth2", "resource": "user", "operation": "delete", "id": "={{$node[\"Zendesk7\"].json[\"id\"]}}"}, "name": "Zendesk12", "type": "n8n-nodes-base.zendesk", "typeVersion": 1, "position": [1150, 600], "credentials": {"zendeskOAuth2Api": {"id": "68", "name": "Zendesk OAuth2 "}}, "id": "e8af8a3e-7cc0-4f30-9498-aca3837919cd"}], "connections": {"Zendesk": {"main": [[{"node": "Zendesk1", "type": "main", "index": 0}]]}, "Zendesk1": {"main": [[{"node": "Zendesk2", "type": "main", "index": 0}]]}, "Zendesk2": {"main": [[{"node": "Zendesk3", "type": "main", "index": 0}]]}, "Zendesk3": {"main": [[{"node": "Zendesk4", "type": "main", "index": 0}]]}, "Zendesk5": {"main": [[{"node": "Zendesk6", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Zendesk", "type": "main", "index": 0}, {"node": "Zendesk5", "type": "main", "index": 0}, {"node": "Zendesk7", "type": "main", "index": 0}]]}, "Zendesk7": {"main": [[{"node": "Zendesk8", "type": "main", "index": 0}]]}, "Zendesk8": {"main": [[{"node": "Zendesk9", "type": "main", "index": 0}]]}, "Zendesk9": {"main": [[{"node": "Zendesk10", "type": "main", "index": 0}]]}, "Zendesk10": {"main": [[{"node": "Zendesk11", "type": "main", "index": 0}]]}, "Zendesk11": {"main": [[{"node": "Zendesk12", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}