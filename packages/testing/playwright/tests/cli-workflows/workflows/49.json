{"createdAt": "2021-02-19T16:46:19.188Z", "updatedAt": "2021-02-19T16:46:33.650Z", "id": "49", "name": "Bitly:Link:create get update", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [300, 300], "id": "2780ec3a-4691-4fb0-b10a-71c14b669e40"}, {"parameters": {"longUrl": "https://n8n.io/", "additionalFields": {"title": "n8n"}}, "name": "Bitly", "type": "n8n-nodes-base.bitly", "typeVersion": 1, "position": [560, 300], "credentials": {"bitlyApi": {"id": "34", "name": "Bitly creds"}}, "id": "f8d1516e-50b7-475e-9745-9b5b6bc8219c"}, {"parameters": {"operation": "get", "id": "={{$node[\"Bitly\"].json[\"id\"]}}"}, "name": "Bitly1", "type": "n8n-nodes-base.bitly", "typeVersion": 1, "position": [730, 300], "credentials": {"bitlyApi": {"id": "34", "name": "Bitly creds"}}, "id": "26aef02a-14df-4496-81c4-6f241501e42b"}, {"parameters": {"operation": "update", "id": "={{$node[\"Bitly\"].json[\"id\"]}}", "updateFields": {"group": "Bl2jgvxfKYU"}}, "name": "Bitly2", "type": "n8n-nodes-base.bitly", "typeVersion": 1, "position": [890, 300], "credentials": {"bitlyApi": {"id": "34", "name": "Bitly creds"}}, "id": "d661eb9e-7481-4724-93b5-39e25d0f3e9f"}], "connections": {"Bitly": {"main": [[{"node": "Bitly1", "type": "main", "index": 0}]]}, "Bitly1": {"main": [[{"node": "Bitly2", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Bitly", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}