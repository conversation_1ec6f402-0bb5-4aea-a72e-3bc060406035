{"createdAt": "2021-04-26T17:42:22.794Z", "updatedAt": "2021-05-21T12:39:59.740Z", "id": "186", "name": "Salesforce:Opportunity:create get addNote getAll getSummary update delete:Task:create get getAll getSummary update delete:Query:search", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "0c8d4ebe-b490-4418-a240-32e15a25879c"}, {"parameters": {"resource": "opportunity", "name": "=Opp{{Date.now()}}", "closeDate": "2021-03-31T22:00:00.000Z", "stageName": "Value Proposition", "additionalFields": {}}, "name": "Salesforce", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [500, 250], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "611612e2-cd0e-413c-a8c0-ca011830d3f3"}, {"parameters": {"resource": "opportunity", "operation": "get", "opportunityId": "={{$node[\"Salesforce\"].json[\"id\"]}}"}, "name": "Salesforce1", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [780, 250], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "76c5bb71-c7a5-41fe-bab8-8ab5a4e3bd02"}, {"parameters": {"resource": "opportunity", "operation": "addNote", "opportunityId": "={{$node[\"Salesforce\"].json[\"id\"]}}", "title": "OppNote", "options": {}}, "name": "Salesforce2", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [910, 250], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "863c2685-0e4a-45ae-a838-570cd2b328be"}, {"parameters": {"resource": "opportunity", "operation": "getSummary"}, "name": "Salesforce3", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1180, 250], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "531dba66-59fa-4274-abd9-fa424c6068d2"}, {"parameters": {"resource": "opportunity", "operation": "getAll", "limit": 1, "options": {}}, "name": "Salesforce4", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1330, 250], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "95bef426-8b35-43d0-a0cb-274650ee2f64"}, {"parameters": {"resource": "opportunity", "operation": "update", "opportunityId": "={{$node[\"Salesforce\"].json[\"id\"]}}", "updateFields": {"name": "UpdatedOpp"}}, "name": "Salesforce5", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1580, 250], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "9806aa1f-fe25-4e09-83c7-7412d6897d52"}, {"parameters": {"resource": "opportunity", "operation": "delete", "opportunityId": "={{$node[\"Salesforce\"].json[\"id\"]}}"}, "name": "Salesforce6", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1730, 250], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "7e5cf8e8-0935-4ea0-b58e-896cd0a1d7a2"}, {"parameters": {"resource": "search", "query": "SELECT Id, Name, BillingCity FROM Account LIMIT 1"}, "name": "Salesforce7", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [500, 400], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "b338d5c8-ce76-4faa-9764-f86e4b346b87"}, {"parameters": {"resource": "task", "status": "In Progress", "additionalFields": {}}, "name": "Salesforce8", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [600, 550], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "2c2811e3-2dc0-42c1-ab13-0940503318fd"}, {"parameters": {"resource": "task", "operation": "get", "taskId": "={{$node[\"Salesforce8\"].json[\"id\"]}}"}, "name": "Salesforce9", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [750, 550], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "0e61e836-069b-45f3-8ea7-298610c170b2"}, {"parameters": {"resource": "task", "operation": "getSummary"}, "name": "Salesforce10", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1010, 550], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "ff0c0a0f-16c9-44e5-9aef-531039f7985c"}, {"parameters": {"resource": "task", "operation": "getAll", "limit": 1, "options": {}}, "name": "Salesforce11", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1150, 550], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "cbf534bd-9db5-4e10-abfa-8cae05cf147b"}, {"parameters": {"resource": "task", "operation": "update", "taskId": "={{$node[\"Salesforce8\"].json[\"id\"]}}", "updateFields": {"status": "Completed"}}, "name": "Salesforce12", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1460, 550], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "04afbeb0-d437-4060-8966-e35cef872f79"}, {"parameters": {"resource": "task", "operation": "delete", "taskId": "={{$node[\"Salesforce8\"].json[\"id\"]}}"}, "name": "Salesforce13", "type": "n8n-nodes-base.salesforce", "typeVersion": 1, "position": [1610, 550], "credentials": {"salesforceOAuth2Api": {"id": "149", "name": "Salesforce OAuth2 API creds"}}, "id": "5f800e5f-7be2-483a-8633-70e258e9ce69"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second", "type": "n8n-nodes-base.function", "position": [640, 250], "typeVersion": 1, "id": "74e93e34-a272-40a3-a976-98f7da373e49"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second1", "type": "n8n-nodes-base.function", "position": [1050, 250], "typeVersion": 1, "id": "c4d53540-e46f-4e9f-b61a-cdfa72df1730"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second2", "type": "n8n-nodes-base.function", "position": [1450, 250], "typeVersion": 1, "id": "1fc2b051-cd25-4fc4-8c6f-819c24ca1f49"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second3", "type": "n8n-nodes-base.function", "position": [880, 550], "typeVersion": 1, "id": "a7794510-cd2c-4265-b7ce-505aceb813c8"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second4", "type": "n8n-nodes-base.function", "position": [480, 550], "typeVersion": 1, "id": "f50da613-c2c9-4823-9ba8-2969ed4899c1"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(500);\n\n// Output data\nreturn items;"}, "name": "Sleep 0.5 second5", "type": "n8n-nodes-base.function", "position": [1290, 550], "typeVersion": 1, "id": "3259acd2-fd46-4348-b7d0-48d4396041df"}], "connections": {"Salesforce": {"main": [[{"node": "Sleep 0.5 second", "type": "main", "index": 0}]]}, "Salesforce1": {"main": [[{"node": "Salesforce2", "type": "main", "index": 0}]]}, "Salesforce3": {"main": [[{"node": "Salesforce4", "type": "main", "index": 0}]]}, "Salesforce4": {"main": [[{"node": "Sleep 0.5 second2", "type": "main", "index": 0}]]}, "Salesforce5": {"main": [[{"node": "Salesforce6", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Salesforce", "type": "main", "index": 0}, {"node": "Salesforce7", "type": "main", "index": 0}, {"node": "Sleep 0.5 second4", "type": "main", "index": 0}]]}, "Salesforce8": {"main": [[{"node": "Salesforce9", "type": "main", "index": 0}]]}, "Salesforce9": {"main": [[{"node": "Sleep 0.5 second3", "type": "main", "index": 0}]]}, "Salesforce10": {"main": [[{"node": "Salesforce11", "type": "main", "index": 0}]]}, "Salesforce11": {"main": [[{"node": "Sleep 0.5 second5", "type": "main", "index": 0}]]}, "Salesforce12": {"main": [[{"node": "Salesforce13", "type": "main", "index": 0}]]}, "Sleep 0.5 second4": {"main": [[{"node": "Salesforce8", "type": "main", "index": 0}]]}, "Sleep 0.5 second1": {"main": [[{"node": "Salesforce3", "type": "main", "index": 0}]]}, "Sleep 0.5 second": {"main": [[{"node": "Salesforce1", "type": "main", "index": 0}]]}, "Sleep 0.5 second3": {"main": [[{"node": "Salesforce10", "type": "main", "index": 0}]]}, "Sleep 0.5 second2": {"main": [[{"node": "Salesforce5", "type": "main", "index": 0}]]}, "Sleep 0.5 second5": {"main": [[{"node": "Salesforce12", "type": "main", "index": 0}]]}, "Salesforce2": {"main": [[{"node": "Sleep 0.5 second1", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}