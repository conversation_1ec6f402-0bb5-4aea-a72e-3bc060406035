{"createdAt": "2021-02-18T10:30:52.070Z", "updatedAt": "2021-06-08T08:12:16.812Z", "id": "39", "name": "Github:Repository:get getProfile getLicense listPopularPaths listReferrers:File:create edit get delete:Issue:create createComment edit get lock:Release:create get getAll update delete:User:getRepositories invite:Review:create getAll get update", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [180, 750], "id": "f0955b0a-3d0c-48ef-adb2-541915a9acef"}, {"parameters": {"resource": "repository", "operation": "getProfile", "owner": "nodemationqa", "repository": "nodeQA"}, "name": "GitHub", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [620, 300], "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "3d876463-6962-4b4d-b3cb-9015ec6848c0"}, {"parameters": {"resource": "repository", "operation": "getLicense", "owner": "nodemationqa", "repository": "nodeQA"}, "name": "GitHub1", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [770, 300], "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "36bbea35-9d6e-4b28-a5a9-53cab2b5d970"}, {"parameters": {"resource": "repository", "operation": "listPopularPaths", "owner": "nodemationqa", "repository": "nodeQA"}, "name": "GitHub2", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [920, 300], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "notes": "CAP_RESULTS_LENGTH=1", "id": "f51387a0-836e-4962-90d9-c89190007c49"}, {"parameters": {"resource": "repository", "operation": "listReferrers", "owner": "nodemationqa", "repository": "nodeQA"}, "name": "GitHub3", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [1070, 300], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "notes": "CAP_RESULTS_LENGTH=1", "id": "db3f98fa-c19d-41fc-922f-0e356b150e0d"}, {"parameters": {"resource": "repository", "operation": "get", "owner": "nodemationqa", "repository": "nodeQA"}, "name": "GitHub4", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [470, 300], "executeOnce": false, "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "4827b07a-4982-4c4a-b47a-647b05846306"}, {"parameters": {"resource": "repository", "owner": "nodemationqa", "repository": "nodeQA", "limit": 1, "getRepositoryIssuesFilters": {}}, "name": "GitHub5", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [1210, 300], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "5948497b-3e84-40ba-a87d-dd4da66a208c"}, {"parameters": {"resource": "file", "owner": "nodemationqa", "repository": "nodeQA", "filePath": "=testFile{{Date.now()}}", "fileContent": "Test file content, create file operation", "commitMessage": "=Commited {{(new Date()).toGMTString()}}", "additionalParameters": {"branch": {"branch": "main"}}}, "name": "GitHub6", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [470, 460], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "752ecae8-1820-4e92-a7e0-8382fb63f94b"}, {"parameters": {"resource": "file", "operation": "edit", "owner": "nodemationqa", "repository": "nodeQA", "filePath": "={{$node[\"GitHub6\"].json[\"content\"][\"path\"]}}", "fileContent": "Updated Test file content, create file operation", "commitMessage": "=Updated commit message {{(new Date()).toGMTString()}}", "additionalParameters": {"branch": {"branch": "main"}}}, "name": "GitHub7", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [630, 460], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "c1326989-6614-42cb-859e-9186e8499ccc"}, {"parameters": {"resource": "file", "operation": "get", "owner": "nodemationqa", "repository": "nodeQA", "filePath": "={{$node[\"GitHub6\"].json[\"content\"][\"path\"]}}", "asBinaryProperty": false}, "name": "GitHub8", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [770, 460], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "2939814a-15b1-4a7b-b829-4b338bdd28a4"}, {"parameters": {"resource": "file", "operation": "delete", "owner": "nodemationqa", "repository": "nodeQA", "filePath": "={{$node[\"GitHub6\"].json[\"content\"][\"path\"]}}", "commitMessage": "=delete commit message {{(new Date()).toGMTString()}}", "additionalParameters": {"branch": {"branch": "main"}}}, "name": "GitHub9", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [910, 460], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "68c193fe-ed71-449d-8f46-08e21cc2f08e"}, {"parameters": {"owner": "nodemationqa", "repository": "nodeQA", "title": "=Test Issue created at {{(new Date()).toGMTString()}}", "body": "=Test issue body  {{(new Date()).toGMTString()}}", "labels": [], "assignees": []}, "name": "GitHub10", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [470, 620], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "22800dbb-3c5c-4c30-9e94-8f4580c26ba7"}, {"parameters": {"operation": "createComment", "owner": "nodemationqa", "repository": "nodeQA", "issueNumber": "={{$node[\"GitHub10\"].json[\"number\"]}}", "body": "=Comment on issue at {{(new Date()).toGMTString()}}"}, "name": "GitHub11", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [620, 620], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "edaee8cb-6469-4395-8c1d-9f577f5ea262"}, {"parameters": {"operation": "edit", "owner": "nodemationqa", "repository": "nodeQA", "issueNumber": "={{$node[\"GitHub10\"].json[\"number\"]}}", "editFields": {"title": "=Updated {{$node[\"GitHub10\"].json[\"title\"]}}{{Date.now()}}", "body": "=updated Test issue body {{(new Date()).toGMTString()}}"}}, "name": "GitHub12", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [770, 620], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "1b158bcd-d934-4347-8988-ba1e6240a4aa"}, {"parameters": {"operation": "get", "owner": "nodemationqa", "repository": "nodeQA", "issueNumber": "={{$node[\"GitHub10\"].json[\"number\"]}}"}, "name": "GitHub13", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [920, 620], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "7c1c2a47-f0ef-4ba5-8329-0f661e93afe4"}, {"parameters": {"operation": "lock", "owner": "nodemationqa", "repository": "nodeQA", "issueNumber": "={{$node[\"GitHub10\"].json[\"number\"]}}", "lockReason": "spam"}, "name": "GitHub14", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [1060, 620], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "13bb869d-2c1e-433d-a4df-e4b7ed1fe15a"}, {"parameters": {"resource": "release", "owner": "nodemationqa", "repository": "nodeQA", "releaseTag": "=Release{{Date.now()}}", "additionalFields": {"name": "=Release {{(new Date()).toGMTString()}}", "draft": true}}, "name": "GitHub15", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [470, 770], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "83a9cc6c-815c-476d-a8d5-83a9475daca8"}, {"parameters": {"resource": "user", "owner": "nodemationqa"}, "name": "GitHub16", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [470, 930], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "ba63e2c6-4ac4-4180-9325-2f61e77f8a7d"}, {"parameters": {"resource": "user", "operation": "invite", "organization": "OrgnodeQA", "email": "<EMAIL>"}, "name": "GitHub17", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [660, 930], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "ce44c4b5-9ed1-463a-bed2-85102bdb66e2"}, {"parameters": {"resource": "review", "owner": "nodemationqa", "repository": "nodeQA", "pullRequestNumber": 3, "event": "comment", "body": "=Review Test {{(new Date()).toGMTString()}}", "additionalFields": {}}, "name": "GitHub18", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [460, 1080], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "dd259704-d7e5-4a3b-8368-dc65c7241b61"}, {"parameters": {"resource": "review", "operation": "getAll", "owner": "nodemationqa", "repository": "nodeQA", "pullRequestNumber": 3, "limit": 1}, "name": "GitHub19", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [610, 1080], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "1c090f0d-1bad-48d7-86ed-17a63359ace2"}, {"parameters": {"resource": "review", "operation": "get", "owner": "nodemationqa", "repository": "nodeQA", "pullRequestNumber": 3, "reviewId": "={{$node[\"GitHub18\"].json[\"id\"]}}"}, "name": "GitHub20", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [750, 1080], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "d917c74c-a3c8-4a12-b4cb-72987ad19f65"}, {"parameters": {"resource": "review", "operation": "update", "owner": "nodemationqa", "repository": "nodeQA", "pullRequestNumber": 3, "reviewId": "={{$node[\"GitHub18\"].json[\"id\"]}}", "body": "=Updated {{$node[\"GitHub18\"].json[\"body\"]}}"}, "name": "GitHub21", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [900, 1080], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "5796a57b-9580-47af-83c2-6770f542eef2"}, {"parameters": {"resource": "release", "operation": "getAll", "owner": "nodemationqa", "repository": "nodeQA", "limit": 1}, "name": "GitHub22", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [620, 770], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "c4ce2f6d-7e2c-4931-9a62-f1702168fdca"}, {"parameters": {"resource": "release", "operation": "get", "owner": "nodemationqa", "repository": "nodeQA", "release_id": "={{$node[\"GitHub15\"].json[\"id\"]}}"}, "name": "GitHub23", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [770, 770], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "d286eac6-e189-41d9-8956-3e528219dfc1"}, {"parameters": {"resource": "release", "operation": "update", "owner": "nodemationqa", "repository": "nodeQA", "release_id": "={{$node[\"GitHub15\"].json[\"id\"]}}", "additionalFields": {"draft": true, "name": "=Updated{{$node[\"GitHub15\"].json[\"name\"]}}", "tag_name": "=Updated{{$node[\"GitHub15\"].json[\"tag_name\"]}}"}}, "name": "GitHub24", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [920, 770], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "1bc53e99-0f2f-425d-977a-e27a75cd9dbf"}, {"parameters": {"resource": "release", "operation": "delete", "owner": "nodemationqa", "repository": "nodeQA", "release_id": "={{$node[\"GitHub15\"].json[\"id\"]}}"}, "name": "GitHub25", "type": "n8n-nodes-base.github", "typeVersion": 1, "position": [1060, 770], "alwaysOutputData": true, "credentials": {"githubApi": {"id": "21", "name": "<PERSON><PERSON><PERSON> creds"}}, "id": "ceab4826-8e38-4a8b-b60c-7dfcf65b7aa9"}], "connections": {"Start": {"main": [[{"node": "GitHub4", "type": "main", "index": 0}, {"node": "GitHub6", "type": "main", "index": 0}, {"node": "GitHub10", "type": "main", "index": 0}, {"node": "GitHub15", "type": "main", "index": 0}, {"node": "GitHub16", "type": "main", "index": 0}, {"node": "GitHub18", "type": "main", "index": 0}]]}, "GitHub": {"main": [[{"node": "GitHub1", "type": "main", "index": 0}]]}, "GitHub1": {"main": [[{"node": "GitHub2", "type": "main", "index": 0}]]}, "GitHub2": {"main": [[{"node": "GitHub3", "type": "main", "index": 0}]]}, "GitHub3": {"main": [[{"node": "GitHub5", "type": "main", "index": 0}]]}, "GitHub4": {"main": [[{"node": "GitHub", "type": "main", "index": 0}]]}, "GitHub8": {"main": [[{"node": "GitHub9", "type": "main", "index": 0}]]}, "GitHub6": {"main": [[{"node": "GitHub7", "type": "main", "index": 0}]]}, "GitHub7": {"main": [[{"node": "GitHub8", "type": "main", "index": 0}]]}, "GitHub10": {"main": [[{"node": "GitHub11", "type": "main", "index": 0}]]}, "GitHub11": {"main": [[{"node": "GitHub12", "type": "main", "index": 0}]]}, "GitHub12": {"main": [[{"node": "GitHub13", "type": "main", "index": 0}]]}, "GitHub13": {"main": [[{"node": "GitHub14", "type": "main", "index": 0}]]}, "GitHub16": {"main": [[{"node": "GitHub17", "type": "main", "index": 0}]]}, "GitHub18": {"main": [[{"node": "GitHub19", "type": "main", "index": 0}]]}, "GitHub19": {"main": [[{"node": "GitHub20", "type": "main", "index": 0}]]}, "GitHub20": {"main": [[{"node": "GitHub21", "type": "main", "index": 0}]]}, "GitHub15": {"main": [[{"node": "GitHub22", "type": "main", "index": 0}]]}, "GitHub22": {"main": [[{"node": "GitHub23", "type": "main", "index": 0}]]}, "GitHub23": {"main": [[{"node": "GitHub24", "type": "main", "index": 0}]]}, "GitHub24": {"main": [[{"node": "GitHub25", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}