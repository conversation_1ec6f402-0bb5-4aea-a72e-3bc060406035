{"createdAt": "2021-02-25T09:24:41.132Z", "updatedAt": "2021-02-25T09:24:41.132Z", "id": "64", "name": "SIGNL4:Al<PERSON>:send resolve", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "e54d4fc1-752f-436e-8c86-139e373a54de"}, {"parameters": {"message": "=test{{Date.now()}}", "additionalFields": {"title": "=Title{{Date.now()}}"}}, "name": "SIGNL4", "type": "n8n-nodes-base.signl4", "typeVersion": 1, "position": [450, 300], "credentials": {"signl4Api": {"id": "53", "name": "Signal4 creds"}}, "id": "870742ca-8ae9-44f6-a2e0-b6ec40298cd2"}, {"parameters": {"operation": "resolve", "externalId": "={{$node[\"SIGNL4\"].json[\"eventId\"]}}"}, "name": "SIGNL", "type": "n8n-nodes-base.signl4", "typeVersion": 1, "position": [600, 300], "credentials": {"signl4Api": {"id": "53", "name": "Signal4 creds"}}, "id": "95ab047b-4159-45fd-9ec6-2d1b9c326a5f"}], "connections": {"SIGNL4": {"main": [[{"node": "SIGNL", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "SIGNL4", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}