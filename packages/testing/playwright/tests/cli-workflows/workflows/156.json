{"createdAt": "2021-03-25T10:56:20.715Z", "updatedAt": "2021-03-25T10:56:20.715Z", "id": "156", "name": "AWSSNS:publish", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "b5a64e20-a0a9-4ed0-8eb2-f648f8f12789"}, {"parameters": {"topic": "arn:aws:sns:us-east-1:589875339869:TestingTopic", "subject": "=Subject{{Date.now()}}", "message": "=Message{{Date.now()}}"}, "name": "AWS SNS", "type": "n8n-nodes-base.awsSns", "typeVersion": 1, "position": [450, 300], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "f57aef0c-0bb4-4e26-b9c2-89df3c72675c"}], "connections": {"Start": {"main": [[{"node": "AWS SNS", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}