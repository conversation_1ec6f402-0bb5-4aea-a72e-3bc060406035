{"name": "Nested sub-node errors:model", "nodes": [{"parameters": {"promptType": "define", "text": "What happened yesterday?", "options": {}}, "type": "@n8n/n8n-nodes-langchain.agent", "typeVersion": 1.7, "position": [140, -60], "id": "17f481ed-343f-4e0f-ace0-ec1a6937f57d", "name": "AI Agent"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [100, 180], "id": "af60b8a2-f065-46e5-92c6-2b3f54e8b48d", "name": "OpenAI Chat Model", "credentials": {"openAiApi": {"id": "Zak03cqeLUOsgkFI", "name": "OpenAi account"}}}, {"parameters": {"name": "search_vector_store", "description": "Retrieves data about past events"}, "type": "@n8n/n8n-nodes-langchain.toolVectorStore", "typeVersion": 1, "position": [400, 160], "id": "3d2dc985-7dc5-4996-90c5-85e777070315", "name": "Vector Store Tool"}, {"parameters": {"options": {}}, "type": "@n8n/n8n-nodes-langchain.embeddingsOpenAi", "typeVersion": 1.1, "position": [360, 520], "id": "55cfa84c-b2f6-40aa-9eef-43fec740ba6c", "name": "Embeddings OpenAI", "credentials": {"openAiApi": {"id": "Zak03cqeLUOsgkFI", "name": "OpenAi account"}}}, {"parameters": {"model": "=gpt-4o-mini123", "options": {}}, "type": "@n8n/n8n-nodes-langchain.lmChatOpenAi", "typeVersion": 1, "position": [620, 360], "id": "46468ea7-318b-4893-b387-b8cc1cca1355", "name": "OpenAI Chat Model1", "credentials": {"openAiApi": {"id": "Zak03cqeLUOsgkFI", "name": "OpenAi account"}}}, {"parameters": {}, "type": "@n8n/n8n-nodes-langchain.vectorStoreInMemory", "typeVersion": 1, "position": [300, 320], "id": "e3a35d71-b47d-46fc-976e-eb671cc9c3fc", "name": "In-Memory Vector Store"}, {"parameters": {}, "type": "n8n-nodes-base.manualTrigger", "typeVersion": 1, "position": [-60, -60], "id": "b42f6a79-4b29-4e17-95ab-5c4fbeda3683", "name": "When clicking ‘Test workflow’"}], "pinData": {}, "connections": {"OpenAI Chat Model": {"ai_languageModel": [[{"node": "AI Agent", "type": "ai_languageModel", "index": 0}]]}, "Vector Store Tool": {"ai_tool": [[{"node": "AI Agent", "type": "ai_tool", "index": 0}]]}, "Embeddings OpenAI": {"ai_embedding": [[{"node": "In-Memory Vector Store", "type": "ai_embedding", "index": 0}]]}, "OpenAI Chat Model1": {"ai_languageModel": [[{"node": "Vector Store Tool", "type": "ai_languageModel", "index": 0}]]}, "In-Memory Vector Store": {"ai_vectorStore": [[{"node": "Vector Store Tool", "type": "ai_vectorStore", "index": 0}]]}, "When clicking ‘Test workflow’": {"main": [[{"node": "AI Agent", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "5b6e4b0f-73c8-4254-8b3e-aa36f3e5ae4e", "meta": {"instanceId": "27cc9b56542ad45b38725555722c50a1c3fee1670bbb67980558314ee08517c4"}, "id": "256", "tags": []}