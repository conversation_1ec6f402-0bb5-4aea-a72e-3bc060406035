{"createdAt": "2021-02-17T11:06:49.772Z", "updatedAt": "2021-05-21T11:22:04.303Z", "id": "29", "name": "ClickUp:Comment:create update getAll delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "31a422ab-f12f-4746-ae7a-bf991eb8cfb2"}, {"parameters": {"resource": "folder", "team": "4651110", "space": "8716115", "name": "=Test2{{Date.now()}}"}, "name": "ClickUp", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [400, 300], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "64ab5323-4dc3-4f28-bdb8-da24f78d8017"}, {"parameters": {"resource": "list", "operation": "create", "team": "4651110", "space": "8716115", "folder": "={{$node[\"ClickUp\"].json[\"id\"]}}", "name": "=testingList2{{Date.now()}}", "additionalFields": {}}, "name": "ClickUp1", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [680, 350], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "f6c32a1e-8187-4e08-8283-a51ec248d1e0"}, {"parameters": {"resource": "comment", "commentOn": "list", "id": "={{$node[\"ClickUp1\"].json[\"id\"]}}", "commentText": "=CommentOnList2{{Date.now()}}", "additionalFields": {}}, "name": "ClickUp2", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [960, 420], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "d27edbe8-e7d6-41bd-b9d0-3baaaeb62946"}, {"parameters": {"resource": "comment", "operation": "update", "comment": "={{$node[\"ClickUp2\"].json[\"id\"]}}", "updateFields": {"commentText": "=commentUpdated{{Date.now()}}"}}, "name": "ClickUp3", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [1230, 420], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "11679d32-a338-4ace-993e-ade4de313f2d"}, {"parameters": {"resource": "comment", "operation": "getAll", "commentsOn": "list", "id": "={{$node[\"ClickUp1\"].json[\"id\"]}}", "limit": 1}, "name": "ClickUp4", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [1520, 420], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "0b4921e6-d3a0-4784-b0ad-bec284c82f0d"}, {"parameters": {"resource": "comment", "operation": "delete", "comment": "={{$node[\"ClickUp4\"].json[\"id\"]}}"}, "name": "ClickUp5", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [1830, 420], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "b714cd49-ad36-44d5-8b3b-366fc2dfc678"}, {"parameters": {"resource": "folder", "operation": "delete", "team": "4651110", "space": "8716115", "folder": "={{$node[\"ClickUp\"].json[\"id\"]}}"}, "name": "ClickUp7", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [2400, 290], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "2db1799e-fc8d-4025-b620-012fca33414a"}, {"parameters": {"resource": "list", "operation": "delete", "team": "4651110", "space": "8716115", "folder": "={{$node[\"ClickUp\"].json[\"id\"]}}", "list": "={{$node[\"ClickUp1\"].json[\"id\"]}}"}, "name": "ClickUp6", "type": "n8n-nodes-base.clickUp", "typeVersion": 1, "position": [2130, 350], "credentials": {"clickUpApi": {"id": "13", "name": "clickup cred"}}, "id": "23a70dfb-25da-4931-80b0-b5dca7d6554d"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds", "type": "n8n-nodes-base.function", "position": [550, 300], "typeVersion": 1, "id": "01532975-dbf4-4757-a52b-255765b578bd"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds1", "type": "n8n-nodes-base.function", "position": [810, 400], "typeVersion": 1, "id": "50e69461-441f-4e30-be11-e2a426d4ec05"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds2", "type": "n8n-nodes-base.function", "position": [1100, 420], "typeVersion": 1, "id": "89191a40-0e4b-482b-a346-c7e04d9dd93f"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds3", "type": "n8n-nodes-base.function", "position": [1370, 420], "typeVersion": 1, "id": "fd4dddb0-7f59-42bb-aac0-07c76dea598e"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds4", "type": "n8n-nodes-base.function", "position": [1670, 420], "typeVersion": 1, "id": "3179d398-f06c-4902-82ac-3689d262297a"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds5", "type": "n8n-nodes-base.function", "position": [2000, 350], "typeVersion": 1, "id": "bfa972c2-e9ac-4500-8ffb-1ede8db29900"}, {"parameters": {"functionCode": "function sleep(milliseconds) {\n  return new Promise(\n    resolve => setTimeout(resolve, milliseconds)\n  );\n}\n\nawait sleep(800);\n\n// Output data\nreturn items;"}, "name": "Sleep 8 Seconds6", "type": "n8n-nodes-base.function", "position": [2270, 290], "typeVersion": 1, "id": "2a6b234c-2bcf-4295-b092-2d7ba6ab7dd1"}], "connections": {"Start": {"main": [[{"node": "ClickUp", "type": "main", "index": 0}]]}, "ClickUp": {"main": [[{"node": "Sleep 8 Seconds", "type": "main", "index": 0}]]}, "ClickUp1": {"main": [[{"node": "Sleep 8 Seconds1", "type": "main", "index": 0}]]}, "ClickUp2": {"main": [[{"node": "Sleep 8 Seconds2", "type": "main", "index": 0}]]}, "ClickUp3": {"main": [[{"node": "Sleep 8 Seconds3", "type": "main", "index": 0}]]}, "ClickUp4": {"main": [[{"node": "Sleep 8 Seconds4", "type": "main", "index": 0}]]}, "ClickUp5": {"main": [[{"node": "Sleep 8 Seconds5", "type": "main", "index": 0}]]}, "ClickUp6": {"main": [[{"node": "Sleep 8 Seconds6", "type": "main", "index": 0}]]}, "Sleep 8 Seconds": {"main": [[{"node": "ClickUp1", "type": "main", "index": 0}]]}, "Sleep 8 Seconds1": {"main": [[{"node": "ClickUp2", "type": "main", "index": 0}]]}, "Sleep 8 Seconds2": {"main": [[{"node": "ClickUp3", "type": "main", "index": 0}]]}, "Sleep 8 Seconds3": {"main": [[{"node": "ClickUp4", "type": "main", "index": 0}]]}, "Sleep 8 Seconds4": {"main": [[{"node": "ClickUp5", "type": "main", "index": 0}]]}, "Sleep 8 Seconds5": {"main": [[{"node": "ClickUp6", "type": "main", "index": 0}]]}, "Sleep 8 Seconds6": {"main": [[{"node": "ClickUp7", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}