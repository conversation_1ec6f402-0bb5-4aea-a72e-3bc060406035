{"createdAt": "2021-02-17T12:54:18.856Z", "updatedAt": "2021-02-24T12:51:15.701Z", "id": "32", "name": "Drif:Contact:create update get delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "a8373e79-8b14-48ae-9baf-79db4bf1a92a"}, {"parameters": {"email": "=fake{{Date.now()}}@gmail.com", "additionalFields": {"name": "=Test{{Date.now()}}"}}, "name": "Drift ", "type": "n8n-nodes-base.drift", "typeVersion": 1, "position": [500, 300], "credentials": {"driftApi": {"id": "15", "name": "Drift API credentials"}}, "id": "8cd5c238-7feb-4b60-985a-d99db59390bc"}, {"parameters": {"operation": "update", "contactId": "={{$node[\"Drift \"].json[\"id\"]}}", "updateFields": {"name": "=node qtest{{Date.now()}}"}}, "name": "Drift 1", "type": "n8n-nodes-base.drift", "typeVersion": 1, "position": [640, 300], "credentials": {"driftApi": {"id": "15", "name": "Drift API credentials"}}, "id": "480edf8d-c1f6-4196-a659-f7b54c33e3db"}, {"parameters": {"operation": "get", "contactId": "={{$node[\"Drift \"].json[\"id\"]}}"}, "name": "Drift 2", "type": "n8n-nodes-base.drift", "typeVersion": 1, "position": [790, 300], "credentials": {"driftApi": {"id": "15", "name": "Drift API credentials"}}, "id": "d495bd88-0ed2-4016-acea-78deee947cc7"}, {"parameters": {"operation": "delete", "contactId": "={{$node[\"Drift \"].json[\"id\"]}}"}, "name": "Drift 3", "type": "n8n-nodes-base.drift", "typeVersion": 1, "position": [940, 300], "credentials": {"driftApi": {"id": "15", "name": "Drift API credentials"}}, "id": "2dce7e0c-3abd-45c8-a3d2-61edecd7a2bf"}], "connections": {"Drift ": {"main": [[{"node": "Drift 1", "type": "main", "index": 0}]]}, "Drift 1": {"main": [[{"node": "Drift 2", "type": "main", "index": 0}]]}, "Drift 2": {"main": [[{"node": "Drift 3", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Drift ", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}