{"createdAt": "2021-03-25T16:19:55.787Z", "updatedAt": "2021-03-25T16:30:10.075Z", "id": "159", "name": "Tapfiliate:Affiliate:create getAll get delete:ProgramAffiliate:add disapprove approve get getAll:AffiliateMetaData:add update remove", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "35279fb2-20b3-4fb0-b959-7a34dfdd053f"}, {"parameters": {"email": "=fake{{Date.now()}}@gmail.com", "firstname": "=Fname{{Date.now()}}", "lastname": "=Lname{{Date.now()}}", "additionalFields": {}}, "name": "Tapfiliate", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [500, 300], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "c69d2f6e-658d-4372-a697-f7e56b1a0f3e"}, {"parameters": {"operation": "getAll", "limit": 1, "filters": {}}, "name": "Tapfiliate1", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [650, 300], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "448a390b-2bf9-4676-bb8a-a963a6662117"}, {"parameters": {"operation": "get", "affiliateId": "={{$node[\"Tapfiliate\"].json[\"id\"]}}"}, "name": "Tapfiliate2", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [800, 300], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "6d5a78c3-8e56-4401-ada4-2de21b0710c6"}, {"parameters": {"operation": "delete", "affiliateId": "={{$node[\"Tapfiliate\"].json[\"id\"]}}"}, "name": "Tapfiliate3", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [1850, 300], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "b30f84fe-d53f-485a-8362-509e66c19060"}, {"parameters": {"resource": "affiliateMetadata", "affiliateId": "={{$node[\"Tapfiliate\"].json[\"id\"]}}", "metadataUi": {"metadataValues": [{"key": "source", "value": "n8n"}]}}, "name": "Tapfiliate4", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [950, 400], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "a6109eae-d935-48c9-a9aa-b06f15f604c9"}, {"parameters": {"resource": "affiliateMetadata", "operation": "update", "affiliateId": "={{$node[\"Tapfiliate\"].json[\"id\"]}}", "key": "source", "value": "n8n.io"}, "name": "Tapfiliate5", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [1100, 400], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "0492bf9a-1a4b-4282-8a07-1319fe026e45"}, {"parameters": {"resource": "affiliateMetadata", "operation": "remove", "affiliateId": "={{$node[\"Tapfiliate\"].json[\"id\"]}}", "key": "source"}, "name": "Tapfiliate6", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [1250, 400], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "baf74150-4967-4c31-8bbf-602ffdeef566"}, {"parameters": {"resource": "programAffiliate", "programId": "nodeqa-affiliate", "affiliateId": "={{$node[\"Tapfiliate\"].json[\"id\"]}}", "additionalFields": {}}, "name": "Tapfiliate7", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [950, 200], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "c909217e-f419-46b9-83fd-7a3b7c8203dc"}, {"parameters": {"resource": "programAffiliate", "operation": "disapprove", "programId": "nodeqa-affiliate", "affiliateId": "={{$node[\"Tapfiliate\"].json[\"id\"]}}"}, "name": "Tapfiliate8", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [1100, 200], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "9178e7d7-d054-466d-83c3-e99b09705035"}, {"parameters": {"resource": "programAffiliate", "operation": "approve", "programId": "nodeqa-affiliate", "affiliateId": "={{$node[\"Tapfiliate\"].json[\"id\"]}}"}, "name": "Tapfiliate9", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [1250, 200], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "8c873324-9d69-4bc8-97c8-3a3907aff5a2"}, {"parameters": {"resource": "programAffiliate", "operation": "get", "programId": "nodeqa-affiliate", "affiliateId": "={{$node[\"Tapfiliate\"].json[\"id\"]}}"}, "name": "Tapfiliate10", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [1400, 200], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "16dc0791-f3b0-4d70-ba91-8f68eaceb7e0"}, {"parameters": {"resource": "programAffiliate", "operation": "getAll", "programId": "nodeqa-affiliate", "limit": 1, "filters": {}}, "name": "Tapfiliate11", "type": "n8n-nodes-base.tapfiliate", "typeVersion": 1, "position": [1550, 200], "credentials": {"tapfiliateApi": {"id": "125", "name": "Tapfiliate API creds"}}, "id": "56c57083-92db-4e64-97d8-34fc470147de"}, {"parameters": {"mode": "wait"}, "name": "<PERSON><PERSON>", "type": "n8n-nodes-base.merge", "typeVersion": 1, "position": [1700, 300], "id": "8465e4b1-c77d-4522-8f59-df64f4582255"}], "connections": {"Tapfiliate": {"main": [[{"node": "Tapfiliate1", "type": "main", "index": 0}]]}, "Tapfiliate1": {"main": [[{"node": "Tapfiliate2", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Tapfiliate", "type": "main", "index": 0}]]}, "Tapfiliate2": {"main": [[{"node": "Tapfiliate4", "type": "main", "index": 0}, {"node": "Tapfiliate7", "type": "main", "index": 0}]]}, "Tapfiliate4": {"main": [[{"node": "Tapfiliate5", "type": "main", "index": 0}]]}, "Tapfiliate5": {"main": [[{"node": "Tapfiliate6", "type": "main", "index": 0}]]}, "Tapfiliate7": {"main": [[{"node": "Tapfiliate8", "type": "main", "index": 0}]]}, "Tapfiliate8": {"main": [[{"node": "Tapfiliate9", "type": "main", "index": 0}]]}, "Tapfiliate9": {"main": [[{"node": "Tapfiliate10", "type": "main", "index": 0}]]}, "Tapfiliate10": {"main": [[{"node": "Tapfiliate11", "type": "main", "index": 0}]]}, "Tapfiliate11": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Tapfiliate6": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge": {"main": [[{"node": "Tapfiliate3", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}