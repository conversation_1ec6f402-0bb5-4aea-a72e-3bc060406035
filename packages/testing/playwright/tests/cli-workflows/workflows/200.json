{"createdAt": "2021-05-11T07:41:09.199Z", "updatedAt": "2021-05-11T08:09:44.364Z", "id": "200", "name": "Pipedrive:Activity:create get update getAll delete:Deal:create get update duplicate getAll delete:Person:create get update getAll search delete:File:create get download delete:Organization:create get update getAll delete:Note:create get update getAll delete", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "1424805b-9720-4fc3-99a3-96b39b1b2d4c"}, {"parameters": {"resource": "activity", "subject": "=Activity{{(new Date).toISOString()}}", "type": "meeting", "additionalFields": {}}, "name": "Pipedrive", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [500, 200], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "c137d42d-46da-4fea-aa3f-098b56e0828d"}, {"parameters": {"resource": "activity", "operation": "get", "activityId": "={{$node[\"Pipedrive\"].json[\"id\"]}}"}, "name": "Pipedrive1", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [650, 200], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "3f597274-830b-4dcb-b0c6-0f1df94e6e9b"}, {"parameters": {"resource": "activity", "operation": "update", "activityId": "={{$node[\"Pipedrive\"].json[\"id\"]}}", "updateFields": {"done": "1", "note": "Used for testing", "subject": "=Updated{{$node[\"Pipedrive\"].json[\"subject\"]}}", "type": "call"}}, "name": "Pipedrive2", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [800, 200], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "14248e74-036b-4817-92b9-c9f549cb9c4f"}, {"parameters": {"resource": "activity", "operation": "getAll", "limit": 1}, "name": "Pipedrive3", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [950, 200], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "a073500e-83c6-4d69-a205-b053396c213e"}, {"parameters": {"resource": "activity", "operation": "delete", "activityId": "={{$node[\"Pipedrive\"].json[\"id\"]}}"}, "name": "Pipedrive4", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [1100, 200], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "25501736-984e-47c8-890e-187307524392"}, {"parameters": {"title": "=Deal{{(new Date).toISOString()}}", "additionalFields": {"status": "open"}}, "name": "Pipedrive5", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [500, 350], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "dfa82f60-7728-4318-9125-229db86ce994"}, {"parameters": {"operation": "get", "dealId": "={{$node[\"Pipedrive5\"].json[\"id\"]}}"}, "name": "Pipedrive6", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [650, 350], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "ddc83c7e-1434-4f19-963b-08e52484dbb6"}, {"parameters": {"operation": "update", "dealId": "={{$node[\"Pipedrive5\"].json[\"id\"]}}", "updateFields": {"status": "won", "title": "=Updated{{$node[\"Pipedrive5\"].json[\"title\"]}}"}}, "name": "Pipedrive7", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [800, 350], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "00491412-62f0-45b4-ad3b-27cbac938293"}, {"parameters": {"operation": "duplicate", "dealId": "={{$node[\"Pipedrive5\"].json[\"id\"]}}"}, "name": "Pipedrive8", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [950, 350], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "b72444fe-3667-468a-89ef-9bbb6c05ff96"}, {"parameters": {"operation": "getAll", "limit": 1}, "name": "Pipedrive9", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [1100, 350], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "4f5d8819-5573-4214-bdf4-711dd2426277"}, {"parameters": {"operation": "delete", "dealId": "={{$node[\"Pipedrive5\"].json[\"id\"]}}"}, "name": "Pipedrive10", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [1250, 350], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "d9540101-e05b-4599-8e26-283e4da6a4bf"}, {"parameters": {"operation": "delete", "dealId": "={{$node[\"Pipedrive8\"].json[\"id\"]}}"}, "name": "Pipedrive11", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [1400, 350], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "967d2d73-6ae2-46e5-9c9c-3325fd47a0f5"}, {"parameters": {"resource": "file", "additionalFields": {}}, "name": "Pipedrive12", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [800, 500], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "751fb956-96d4-4b31-92c7-34159e7c83d1"}, {"parameters": {"values": {"number": [], "string": [{"name": "file", "value": "=Testing file"}]}, "options": {}}, "name": "Set", "type": "n8n-nodes-base.set", "typeVersion": 1, "position": [500, 500], "id": "aa58167e-6c9e-4be6-9049-f280204d8eba"}, {"parameters": {"mode": "jsonToBinary", "convertAllData": false, "sourceKey": "file", "options": {"fileName": "testFile", "mimeType": "text"}}, "name": "Move Binary Data", "type": "n8n-nodes-base.moveBinaryData", "typeVersion": 1, "position": [650, 500], "id": "3ba06dd7-c49e-4b72-9785-2e5e3fc01f53"}, {"parameters": {"resource": "file", "operation": "get", "fileId": "={{$node[\"Pipedrive12\"].json[\"id\"]}}"}, "name": "Pipedrive13", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [950, 500], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "e769e423-dcff-4d51-8200-3c5a3fbe01e2"}, {"parameters": {"resource": "file", "operation": "download", "fileId": "={{$node[\"Pipedrive12\"].json[\"id\"]}}"}, "name": "Pipedrive14", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [1100, 500], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "c973182a-5d43-4a12-92c2-1ece40b66f22"}, {"parameters": {"resource": "file", "operation": "delete", "fileId": "={{$node[\"Pipedrive12\"].json[\"id\"]}}"}, "name": "Pipedrive15", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [1250, 500], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "0d2cac69-6912-4156-8bb5-57c174a53e82"}, {"parameters": {"resource": "organization", "name": "=Organization_{{(new Date).toISOString()}}", "additionalFields": {"label": 3}}, "name": "Pipedrive16", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [500, 650], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "45b73830-24ac-486f-b1e1-cfa76d621624"}, {"parameters": {"resource": "organization", "operation": "get", "organizationId": "={{$node[\"Pipedrive16\"].json[\"id\"]}}"}, "name": "Pipedrive17", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [670, 650], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "0fd8d1b6-4851-4c91-b3ea-cf7a2759286e"}, {"parameters": {"resource": "organization", "operation": "update", "organizationId": "={{$node[\"Pipedrive16\"].json[\"id\"]}}", "updateFields": {"label": 2, "name": "=Updated{{$node[\"Pipedrive16\"].json[\"name\"]}}", "visible_to": "3"}}, "name": "Pipedrive18", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [820, 650], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "00c38f10-591e-43b4-8e1e-875383f0846b"}, {"parameters": {"resource": "organization", "operation": "getAll", "limit": 1}, "name": "Pipedrive19", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [970, 650], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "c528c1e6-aee8-46ae-a573-b770d61a5462"}, {"parameters": {"resource": "organization", "operation": "delete", "organizationId": "={{$node[\"Pipedrive16\"].json[\"id\"]}}"}, "name": "Pipedrive20", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [1120, 650], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "95f50030-a435-470c-910d-990fd4bde80d"}, {"parameters": {"resource": "note", "content": "=Note_{{(new Date).toISOString()}}", "additionalFields": {"org_id": "={{$node[\"Pipedrive16\"].json[\"id\"]}}"}}, "name": "Pipedrive21", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [650, 800], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "dcdc4d84-f8c2-4402-bfc9-1b38740bc70a"}, {"parameters": {"resource": "note", "operation": "get", "noteId": "={{$node[\"Pipedrive21\"].json[\"id\"]}}"}, "name": "Pipedrive22", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [800, 800], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "4128895d-8656-49fb-a196-099f2ec336a3"}, {"parameters": {"resource": "note", "operation": "update", "noteId": "={{$node[\"Pipedrive21\"].json[\"id\"]}}", "updateFields": {"content": "=Updated{{$node[\"Pipedrive21\"].json[\"content\"]}}"}}, "name": "Pipedrive23", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [950, 800], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "d1900e79-4b29-4f56-9110-311ea321dc78"}, {"parameters": {"resource": "note", "operation": "getAll", "additionalFields": {}, "limit": 1}, "name": "Pipedrive24", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [1100, 800], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "64d156ea-828b-42da-88d5-170e48d1b11b"}, {"parameters": {"resource": "note", "operation": "delete", "noteId": "={{$node[\"Pipedrive21\"].json[\"id\"]}}"}, "name": "Pipedrive25", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [1250, 800], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "40e8dea1-279c-44d6-ae6a-cc79ab5e123b"}, {"parameters": {"resource": "person", "name": "=Person_{{(new Date).toISOString()}}", "additionalFields": {"label": 1, "visible_to": "3"}}, "name": "Pipedrive26", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [500, 50], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "c1b68ae7-37ac-42d5-9ff8-43c88223122a"}, {"parameters": {"resource": "person", "operation": "get", "personId": "={{$node[\"Pipedrive26\"].json[\"id\"]}}"}, "name": "Pipedrive27", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [650, 50], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "c9dcd61f-c456-40e8-ace3-05c9e5f961c7"}, {"parameters": {"resource": "person", "operation": "update", "personId": "={{$node[\"Pipedrive26\"].json[\"id\"]}}", "updateFields": {"label": 2, "name": "=Updated{{$node[\"Pipedrive31302926\"].json[\"first_name\"]}}"}}, "name": "Pipedrive28", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [800, 50], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "04f88fba-4196-400e-9147-760c9253b423"}, {"parameters": {"resource": "person", "operation": "getAll", "limit": 1, "additionalFields": {}}, "name": "Pipedrive29", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [950, 50], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "2e72dc73-d6f5-4641-bb17-15def44c8481"}, {"parameters": {"resource": "person", "operation": "search", "limit": 1, "term": "updated", "additionalFields": {}}, "name": "Pipedrive30", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [1100, 50], "alwaysOutputData": true, "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "3b2617a2-e33d-40fa-8e1b-5a7c402a2898"}, {"parameters": {"resource": "person", "operation": "delete", "personId": "={{$node[\"Pipedrive26\"].json[\"id\"]}}"}, "name": "Pipedrive31", "type": "n8n-nodes-base.pipedrive", "typeVersion": 1, "position": [1250, 50], "credentials": {"pipedriveApi": {"id": "171", "name": "Pipedrive API creds"}}, "id": "094f29de-6d5b-4433-a673-3a5127f8438d"}], "connections": {"Pipedrive": {"main": [[{"node": "Pipedrive1", "type": "main", "index": 0}]]}, "Pipedrive1": {"main": [[{"node": "Pipedrive2", "type": "main", "index": 0}]]}, "Pipedrive2": {"main": [[{"node": "Pipedrive3", "type": "main", "index": 0}]]}, "Pipedrive3": {"main": [[{"node": "Pipedrive4", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Pipedrive", "type": "main", "index": 0}, {"node": "Pipedrive5", "type": "main", "index": 0}, {"node": "Set", "type": "main", "index": 0}, {"node": "Pipedrive16", "type": "main", "index": 0}, {"node": "Pipedrive26", "type": "main", "index": 0}]]}, "Pipedrive5": {"main": [[{"node": "Pipedrive6", "type": "main", "index": 0}]]}, "Pipedrive6": {"main": [[{"node": "Pipedrive7", "type": "main", "index": 0}]]}, "Pipedrive7": {"main": [[{"node": "Pipedrive8", "type": "main", "index": 0}]]}, "Pipedrive8": {"main": [[{"node": "Pipedrive9", "type": "main", "index": 0}]]}, "Pipedrive9": {"main": [[{"node": "Pipedrive10", "type": "main", "index": 0}]]}, "Pipedrive10": {"main": [[{"node": "Pipedrive11", "type": "main", "index": 0}]]}, "Set": {"main": [[{"node": "Move Binary Data", "type": "main", "index": 0}]]}, "Move Binary Data": {"main": [[{"node": "Pipedrive12", "type": "main", "index": 0}]]}, "Pipedrive12": {"main": [[{"node": "Pipedrive13", "type": "main", "index": 0}]]}, "Pipedrive13": {"main": [[{"node": "Pipedrive14", "type": "main", "index": 0}]]}, "Pipedrive14": {"main": [[{"node": "Pipedrive15", "type": "main", "index": 0}]]}, "Pipedrive16": {"main": [[{"node": "Pipedrive21", "type": "main", "index": 0}]]}, "Pipedrive17": {"main": [[{"node": "Pipedrive18", "type": "main", "index": 0}]]}, "Pipedrive18": {"main": [[{"node": "Pipedrive19", "type": "main", "index": 0}]]}, "Pipedrive19": {"main": [[{"node": "Pipedrive20", "type": "main", "index": 0}]]}, "Pipedrive21": {"main": [[{"node": "Pipedrive22", "type": "main", "index": 0}]]}, "Pipedrive22": {"main": [[{"node": "Pipedrive23", "type": "main", "index": 0}]]}, "Pipedrive23": {"main": [[{"node": "Pipedrive24", "type": "main", "index": 0}]]}, "Pipedrive24": {"main": [[{"node": "Pipedrive25", "type": "main", "index": 0}]]}, "Pipedrive25": {"main": [[{"node": "Pipedrive17", "type": "main", "index": 0}]]}, "Pipedrive26": {"main": [[{"node": "Pipedrive27", "type": "main", "index": 0}]]}, "Pipedrive27": {"main": [[{"node": "Pipedrive28", "type": "main", "index": 0}]]}, "Pipedrive28": {"main": [[{"node": "Pipedrive29", "type": "main", "index": 0}]]}, "Pipedrive29": {"main": [[{"node": "Pipedrive30", "type": "main", "index": 0}]]}, "Pipedrive30": {"main": [[{"node": "Pipedrive31", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}