{"createdAt": "2021-03-09T14:05:33.799Z", "updatedAt": "2021-03-09T14:15:46.076Z", "id": "112", "name": "Rabbitmq:queue:exchange", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "ee0f6961-37a7-486d-9d75-f73aa8708750"}, {"parameters": {"queue": "=SimpleQueue", "sendInputData": false, "message": "=Message{{Date.now()}}", "options": {"autoDelete": true, "durable": false}}, "name": "RabbitMQ", "type": "n8n-nodes-base.rabbitmq", "typeVersion": 1, "position": [500, 200], "credentials": {"rabbitmq": {"id": "79", "name": "RabbitMQ creds"}}, "id": "3b3b66f0-91ab-4a41-a2ab-fec7ce0e36e4"}, {"parameters": {"mode": "exchange", "exchange": "FanoutExchange", "routingKey": "test", "sendInputData": false, "message": "=FanoutMessage{{Date.now()}}", "options": {"durable": false}}, "name": "RabbitMQ1", "type": "n8n-nodes-base.rabbitmq", "typeVersion": 1, "position": [500, 350], "credentials": {"rabbitmq": {"id": "79", "name": "RabbitMQ creds"}}, "id": "f4bb81a0-f6c2-4194-b8ec-0fe6f2350384"}, {"parameters": {"mode": "exchange", "exchange": "DirectExchange", "exchangeType": "direct", "routingKey": "test", "sendInputData": false, "message": "=DirectMessage{{Date.now()}}", "options": {"durable": false}}, "name": "RabbitMQ2", "type": "n8n-nodes-base.rabbitmq", "typeVersion": 1, "position": [650, 350], "credentials": {"rabbitmq": {"id": "79", "name": "RabbitMQ creds"}}, "id": "ecd77a9f-6523-445d-a7b1-e1585c652c55"}, {"parameters": {"mode": "exchange", "exchange": "TopicExchange", "exchangeType": "topic", "routingKey": "test", "sendInputData": false, "message": "=TopicMessage{{Date.now()}}", "options": {"durable": false}}, "name": "RabbitMQ3", "type": "n8n-nodes-base.rabbitmq", "typeVersion": 1, "position": [800, 350], "credentials": {"rabbitmq": {"id": "79", "name": "RabbitMQ creds"}}, "id": "bd8becb0-8c2b-4516-b375-ac005bfe9a18"}, {"parameters": {"mode": "exchange", "exchange": "HeadersExchange", "exchangeType": "headers", "routingKey": "test", "sendInputData": false, "message": "=HeadersMessage{{Date.now()}}", "options": {"durable": false}}, "name": "RabbitMQ4", "type": "n8n-nodes-base.rabbitmq", "typeVersion": 1, "position": [950, 350], "credentials": {"rabbitmq": {"id": "79", "name": "RabbitMQ creds"}}, "id": "abdcf5b6-21ef-40da-b58d-3a2f9ad3b676"}], "connections": {"Start": {"main": [[{"node": "RabbitMQ", "type": "main", "index": 0}, {"node": "RabbitMQ1", "type": "main", "index": 0}]]}, "RabbitMQ1": {"main": [[{"node": "RabbitMQ2", "type": "main", "index": 0}]]}, "RabbitMQ2": {"main": [[{"node": "RabbitMQ3", "type": "main", "index": 0}]]}, "RabbitMQ3": {"main": [[{"node": "RabbitMQ4", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}