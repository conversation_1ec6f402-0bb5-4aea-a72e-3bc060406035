{"createdAt": "2021-04-23T08:55:02.078Z", "updatedAt": "2021-04-23T08:55:02.078Z", "id": "182", "name": "Webflow:Item:create get update delete getAll", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "a462a0c7-cda9-4a9e-b447-99be5b8df6f2"}, {"parameters": {"operation": "create", "siteId": "6082884ac940c371277833ae", "collectionId": "608289f18593d40792d70d54", "fieldsUi": {"fieldValues": [{"fieldId": "name", "fieldValue": "=Item{{Date.now()}}"}, {"fieldId": "slug", "fieldValue": "=ItemSlug{{Date.now()}}"}, {"fieldId": "_archived", "fieldValue": "False"}, {"fieldId": "_draft", "fieldValue": "True"}]}}, "name": "Webflow", "type": "n8n-nodes-base.webflow", "typeVersion": 1, "position": [450, 300], "credentials": {"webflowApi": {"id": "147", "name": "Webflow API creds"}}, "id": "270ca4f8-df14-4629-95df-e5adb713d02d"}, {"parameters": {"siteId": "6082884ac940c371277833ae", "collectionId": "608289f18593d40792d70d54", "itemId": "={{$node[\"Webflow\"].json[\"_id\"]}}"}, "name": "Webflow1", "type": "n8n-nodes-base.webflow", "typeVersion": 1, "position": [650, 300], "credentials": {"webflowApi": {"id": "147", "name": "Webflow API creds"}}, "id": "fc5b71c8-e124-49aa-b15f-8fed15916ff1"}, {"parameters": {"operation": "update", "siteId": "6082884ac940c371277833ae", "collectionId": "608289f18593d40792d70d54", "itemId": "={{$node[\"Webflow\"].json[\"_id\"]}}", "fieldsUi": {"fieldValues": [{"fieldId": "_draft", "fieldValue": "False"}, {"fieldId": "_archived", "fieldValue": "False"}, {"fieldId": "slug", "fieldValue": "=Updated{{$node[\"Webflow1\"].json[\"slug\"]}}"}, {"fieldId": "name", "fieldValue": "=Updated{{$node[\"Webflow1\"].json[\"name\"]}}"}]}}, "name": "Webflow2", "type": "n8n-nodes-base.webflow", "typeVersion": 1, "position": [850, 300], "credentials": {"webflowApi": {"id": "147", "name": "Webflow API creds"}}, "id": "07182187-0587-41b2-89e0-1a289070643d"}, {"parameters": {"operation": "delete", "siteId": "6082884ac940c371277833ae", "collectionId": "608289f18593d40792d70d54", "itemId": "={{$node[\"Webflow\"].json[\"_id\"]}}"}, "name": "Webflow3", "type": "n8n-nodes-base.webflow", "typeVersion": 1, "position": [1050, 300], "credentials": {"webflowApi": {"id": "147", "name": "Webflow API creds"}}, "id": "197f83d4-c767-4c4a-a776-6c28ec39c799"}, {"parameters": {"operation": "getAll", "siteId": "6082884ac940c371277833ae", "collectionId": "608289f18593d40792d70d54", "limit": 1}, "name": "Webflow4", "type": "n8n-nodes-base.webflow", "typeVersion": 1, "position": [1250, 300], "credentials": {"webflowApi": {"id": "147", "name": "Webflow API creds"}}, "id": "acb4b42b-c8b1-4222-9227-10b8456505fb"}], "connections": {"Webflow": {"main": [[{"node": "Webflow1", "type": "main", "index": 0}]]}, "Webflow1": {"main": [[{"node": "Webflow2", "type": "main", "index": 0}]]}, "Webflow2": {"main": [[{"node": "Webflow3", "type": "main", "index": 0}]]}, "Webflow3": {"main": [[{"node": "Webflow4", "type": "main", "index": 0}]]}, "Start": {"main": [[{"node": "Webflow", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}