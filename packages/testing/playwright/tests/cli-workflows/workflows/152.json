{"createdAt": "2021-03-25T09:23:15.717Z", "updatedAt": "2021-05-10T14:22:21.201Z", "id": "152", "name": "AWSComprehend:Text:detectDominantLanguage detectSentiment detectEntities", "active": false, "nodes": [{"parameters": {}, "name": "Start", "type": "n8n-nodes-base.start", "typeVersion": 1, "position": [250, 300], "id": "4db01d0f-ffb0-4ced-be9a-8e80eddc20d4"}, {"parameters": {"text": "n8n (pronounced n-eight-n) helps you to interconnect every app with an API in the world with each other to share and manipulate its data without a single line of code. It is an easy to use, user-friendly and highly customizable service, which uses an intuitive user interface for you to design your unique workflows very fast. Hosted on your server and not based in the cloud, it keeps your sensible data very secure in your own trusted database."}, "name": "AWS Comprehend", "type": "n8n-nodes-base.awsComprehend", "typeVersion": 1, "position": [450, 200], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "dbc6fd62-d8c6-44e7-aed9-6f8f28e609db"}, {"parameters": {"operation": "detectSentiment", "text": "n8n (pronounced n-eight-n) helps you to interconnect every app with an API in the world with each other to share and manipulate its data without a single line of code. It is an easy to use, user-friendly and highly customizable service, which uses an intuitive user interface for you to design your unique workflows very fast. Hosted on your server and not based in the cloud, it keeps your sensible data very secure in your own trusted database."}, "name": "AWS Comprehend1", "type": "n8n-nodes-base.awsComprehend", "typeVersion": 1, "position": [450, 380], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "5ba9b2b0-0534-44d9-bdb5-3f45cd4964d9"}, {"parameters": {"operation": "detectEntities", "text": "n8n (pronounced n-eight-n) helps you to interconnect every app with an API in the world with each other to share and manipulate its data without a single line of code. It is an easy to use, user-friendly and highly customizable service, which uses an intuitive user interface for you to design your unique workflows very fast. Hosted on your server and not based in the cloud, it keeps your sensible data very secure in your own trusted database.", "additionalFields": {}}, "name": "AWS Comprehend2", "type": "n8n-nodes-base.awsComprehend", "typeVersion": 1, "position": [450, 550], "credentials": {"aws": {"id": "124", "name": "AWS creds"}}, "id": "c744db52-65cc-4e4a-811b-69b2ec328352"}], "connections": {"Start": {"main": [[{"node": "AWS Comprehend", "type": "main", "index": 0}, {"node": "AWS Comprehend1", "type": "main", "index": 0}, {"node": "AWS Comprehend2", "type": "main", "index": 0}]]}}, "settings": {}, "staticData": null, "meta": null, "pinData": null, "versionId": null, "triggerCount": 0, "tags": []}